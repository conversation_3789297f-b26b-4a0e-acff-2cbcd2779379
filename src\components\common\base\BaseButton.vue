<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <view v-if="loading" class="base-button__loading">
      <view class="loading-spinner" />
    </view>
    
    <view v-if="icon && !loading" class="base-button__icon">
      <image v-if="typeof icon === 'string'" :src="icon" class="icon-image" />
      <text v-else class="icon-text">{{ icon }}</text>
    </view>
    
    <view v-if="$slots.default" class="base-button__content">
      <slot />
    </view>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 按钮类型
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'text'
  // 按钮尺寸
  size?: 'small' | 'medium' | 'large'
  // 是否禁用
  disabled?: boolean
  // 是否加载中
  loading?: boolean
  // 是否块级按钮
  block?: boolean
  // 是否圆形按钮
  round?: boolean
  // 图标
  icon?: string
  // 图标位置
  iconPosition?: 'left' | 'right'
}

interface Emits {
  click: [event: Event]
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  iconPosition: 'left'
})

const emit = defineEmits<Emits>()

// 计算按钮样式类
const buttonClasses = computed(() => {
  return [
    'base-button',
    `base-button--${props.type}`,
    `base-button--${props.size}`,
    {
      'base-button--disabled': props.disabled,
      'base-button--loading': props.loading,
      'base-button--block': props.block,
      'base-button--round': props.round,
      'base-button--icon-right': props.iconPosition === 'right'
    }
  ]
})

// 处理点击事件
const handleClick = (event: Event) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.base-button {
  @include button-base;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-family: inherit;
  font-weight: var(--font-weight-medium);
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  user-select: none;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  
  // 按钮类型样式
  &--primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover:not(.base-button--disabled) {
      background-color: var(--primary-dark);
      border-color: var(--primary-dark);
    }
    
    &:active:not(.base-button--disabled) {
      transform: scale(0.98);
    }
  }
  
  &--secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--separator);
    
    &:hover:not(.base-button--disabled) {
      background-color: var(--bg-tertiary);
    }
  }
  
  &--success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
  }
  
  &--warning {
    background-color: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
  }
  
  &--error {
    background-color: var(--error-color);
    color: white;
    border-color: var(--error-color);
  }
  
  &--text {
    background-color: transparent;
    color: var(--primary-color);
    border-color: transparent;
    
    &:hover:not(.base-button--disabled) {
      background-color: var(--bg-secondary);
    }
  }
  
  // 按钮尺寸
  &--small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    min-height: 32px;
  }
  
  &--medium {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-md);
    border-radius: var(--border-radius-md);
    min-height: 40px;
  }
  
  &--large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
    min-height: 48px;
  }
  
  // 按钮状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &--loading {
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &--block {
    width: 100%;
    display: flex;
  }
  
  &--round {
    border-radius: 50px;
  }
  
  &--icon-right {
    flex-direction: row-reverse;
  }
  
  // 子元素样式
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .icon-image {
      width: 16px;
      height: 16px;
    }
    
    .icon-text {
      font-size: 16px;
    }
  }
  
  &__content {
    flex: 1;
  }
}

// 加载动画
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式适配
@include respond-to('small') {
  .base-button {
    &--small {
      min-height: 28px;
      padding: 6px 12px;
    }
    
    &--medium {
      min-height: 36px;
      padding: 8px 16px;
    }
    
    &--large {
      min-height: 44px;
      padding: 12px 20px;
    }
  }
}
</style>
