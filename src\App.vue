<template>
  <div id="app">
    <!-- UniApp 会自动处理页面路由 -->
  </div>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useAppStore } from '@/stores/modules/app'

const appStore = useAppStore()

onLaunch(() => {
  console.log('App Launch')
  appStore.initialize()
})

onShow(() => {
  console.log('App Show')
  appStore.setAppVisible(true)
})

onHide(() => {
  console.log('App Hide')
  appStore.setAppVisible(false)
})
</script>

<style lang="scss">
@import '@/assets/styles/base.scss';

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
