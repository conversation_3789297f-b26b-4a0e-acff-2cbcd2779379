<template>
  <view class="home-page">
    <!-- 顶部导航 -->
    <view class="home-header">
      <view class="header-content">
        <view class="header-title">
          <text class="title-text">NextNovel</text>
        </view>
        
        <view class="header-actions">
          <view class="action-item" @click="handleSearch">
            <text class="action-icon">🔍</text>
          </view>
          <view class="action-item" @click="handleNotifications">
            <text class="action-icon">🔔</text>
            <view v-if="hasUnreadNotifications" class="notification-badge" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 子页面导航 -->
    <view class="home-tabs">
      <view
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-item', { active: currentTab === tab.key }]"
        @click="switchTab(tab.key)"
      >
        <text class="tab-text">{{ tab.label }}</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="home-content">
      <!-- 聊天页面 -->
      <view v-if="currentTab === 'chat'" class="content-panel">
        <ChatPanel />
      </view>
      
      <!-- 小说页面 -->
      <view v-if="currentTab === 'novel'" class="content-panel">
        <NovelPanel />
      </view>
      
      <!-- 世界页面 -->
      <view v-if="currentTab === 'world'" class="content-panel">
        <WorldPanel />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/modules/app'
import { useUserStore } from '@/stores/modules/user'
import ChatPanel from './components/ChatPanel.vue'
import NovelPanel from './components/NovelPanel.vue'
import WorldPanel from './components/WorldPanel.vue'

// 页面标题
uni.setNavigationBarTitle({
  title: 'NextNovel'
})

const appStore = useAppStore()
const userStore = useUserStore()

// 当前选中的标签页
const currentTab = ref('chat')

// 标签页配置
const tabs = [
  { key: 'chat', label: '聊天' },
  { key: 'novel', label: '小说' },
  { key: 'world', label: '世界' }
]

// 是否有未读通知
const hasUnreadNotifications = computed(() => {
  // TODO: 从通知store获取未读状态
  return false
})

// 切换标签页
const switchTab = (tabKey: string) => {
  currentTab.value = tabKey
  
  // 记录用户行为
  console.log(`Switch to tab: ${tabKey}`)
}

// 搜索功能
const handleSearch = () => {
  appStore.navigateTo('/pages/search/index')
}

// 通知功能
const handleNotifications = () => {
  appStore.navigateTo('/pages/notifications/index')
}

// 页面初始化
onMounted(() => {
  // 初始化应用状态
  if (!appStore.initialized) {
    appStore.initialize()
  }
  
  // 获取用户信息
  if (userStore.isLoggedIn) {
    userStore.fetchStatistics()
  }
})
</script>

<style lang="scss" scoped>
.home-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
}

.home-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--separator);
  padding-top: var(--safe-area-top, 0);
  
  .header-content {
    @include flex-between;
    padding: var(--spacing-md);
    height: 44px;
  }
  
  .header-title {
    .title-text {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
    }
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-sm);
    
    .action-item {
      position: relative;
      @include flex-center;
      width: 32px;
      height: 32px;
      border-radius: var(--border-radius-sm);
      transition: background-color 0.2s ease;
      
      &:active {
        background-color: var(--bg-secondary);
      }
      
      .action-icon {
        font-size: 18px;
      }
      
      .notification-badge {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 8px;
        height: 8px;
        background-color: var(--error-color);
        border-radius: 50%;
      }
    }
  }
}

.home-tabs {
  display: flex;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--separator);
  
  .tab-item {
    flex: 1;
    @include flex-center;
    height: 48px;
    position: relative;
    transition: all 0.2s ease;
    
    .tab-text {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-tertiary);
      transition: color 0.2s ease;
    }
    
    &.active {
      .tab-text {
        color: var(--primary-color);
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 24px;
        height: 2px;
        background-color: var(--primary-color);
        border-radius: 1px;
      }
    }
    
    &:active {
      background-color: var(--bg-secondary);
    }
  }
}

.home-content {
  flex: 1;
  overflow: hidden;
  
  .content-panel {
    height: 100%;
    overflow: hidden;
  }
}

// 响应式适配
@include respond-to('small') {
  .home-header {
    .header-content {
      padding: var(--spacing-sm) var(--spacing-md);
    }
  }
  
  .home-tabs {
    .tab-item {
      height: 44px;
      
      .tab-text {
        font-size: var(--font-size-sm);
      }
    }
  }
}
</style>
