# NEXTNOVEL APP 项目结构和文件组织

## 项目目录结构

```
nextnovel-app/
├── .vscode/                    # VS Code 配置
│   ├── settings.json
│   ├── extensions.json
│   └── launch.json
├── docs/                       # 项目文档
│   ├── 项目概述.md
│   ├── 技术架构.md
│   ├── 开发规范.md
│   ├── 项目结构.md
│   ├── API文档.md
│   └── 部署指南.md
├── public/                     # 静态资源
│   ├── index.html
│   ├── favicon.ico
│   └── manifest.json
├── src/                        # 源代码目录
│   ├── components/             # 组件目录
│   │   ├── common/             # 通用组件
│   │   ├── chat/               # 聊天相关组件
│   │   ├── novel/              # 小说相关组件
│   │   ├── world/              # 世界相关组件
│   │   ├── creation/           # 创作相关组件
│   │   ├── discovery/          # 发现相关组件
│   │   ├── forum/              # 论坛相关组件
│   │   └── profile/            # 个人中心组件
│   ├── pages/                  # 页面组件
│   │   ├── home/               # 首页
│   │   ├── discovery/          # 发现页
│   │   ├── creation/           # 创作页
│   │   ├── forum/              # 论坛页
│   │   └── profile/            # 个人中心
│   ├── stores/                 # 状态管理
│   │   ├── modules/            # Store 模块
│   │   └── index.ts            # Store 入口
│   ├── services/               # 服务层
│   │   ├── api/                # API 服务
│   │   ├── ai/                 # AI 服务
│   │   ├── storage/            # 存储服务
│   │   └── websocket/          # WebSocket 服务
│   ├── utils/                  # 工具函数
│   │   ├── format.ts           # 格式化工具
│   │   ├── validation.ts       # 验证工具
│   │   ├── request.ts          # 请求工具
│   │   └── constants.ts        # 常量定义
│   ├── types/                  # 类型定义
│   │   ├── api.ts              # API 类型
│   │   ├── chat.ts             # 聊天类型
│   │   ├── novel.ts            # 小说类型
│   │   ├── world.ts            # 世界类型
│   │   └── user.ts             # 用户类型
│   ├── assets/                 # 静态资源
│   │   ├── images/             # 图片资源
│   │   ├── icons/              # 图标资源
│   │   ├── fonts/              # 字体资源
│   │   └── styles/             # 样式文件
│   ├── hooks/                  # 自定义 Hooks
│   │   ├── useChat.ts          # 聊天相关 Hook
│   │   ├── useNovel.ts         # 小说相关 Hook
│   │   └── useAuth.ts          # 认证相关 Hook
│   ├── plugins/                # 插件配置
│   │   ├── router.ts           # 路由配置
│   │   └── pinia.ts            # Pinia 配置
│   ├── App.vue                 # 根组件
│   ├── main.ts                 # 应用入口
│   ├── manifest.json           # UniApp 配置
│   ├── pages.json              # 页面配置
│   └── uni.scss                # 全局样式
├── tests/                      # 测试文件
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── e2e/                    # 端到端测试
├── .env                        # 环境变量
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── .gitignore                  # Git 忽略文件
├── .eslintrc.js                # ESLint 配置
├── .prettierrc                 # Prettier 配置
├── tsconfig.json               # TypeScript 配置
├── vite.config.ts              # Vite 配置
├── package.json                # 项目配置
└── README.md                   # 项目说明
```

## 核心目录详解

### 1. src/components/ - 组件目录

#### 组件分类原则
- **common/**: 通用组件，可在多个页面复用
- **业务模块/**: 按业务功能划分的专用组件

#### 组件目录结构
```
components/
├── common/                     # 通用组件
│   ├── base/                   # 基础组件
│   │   ├── BaseButton.vue
│   │   ├── BaseInput.vue
│   │   ├── BaseModal.vue
│   │   └── BaseLoading.vue
│   ├── layout/                 # 布局组件
│   │   ├── AppHeader.vue
│   │   ├── AppFooter.vue
│   │   ├── AppSidebar.vue
│   │   └── AppTabBar.vue
│   ├── form/                   # 表单组件
│   │   ├── FormField.vue
│   │   ├── FormValidator.vue
│   │   └── FormSubmit.vue
│   └── media/                  # 媒体组件
│       ├── ImageViewer.vue
│       ├── AudioPlayer.vue
│       └── VideoPlayer.vue
├── chat/                       # 聊天组件
│   ├── ChatMessageList.vue     # 消息列表
│   ├── ChatMessage.vue         # 单条消息
│   ├── ChatInput.vue           # 输入框
│   ├── ChatAvatar.vue          # 头像组件
│   ├── CharacterCard.vue       # 角色卡片
│   └── GroupChatPanel.vue      # 群聊面板
├── novel/                      # 小说组件
│   ├── NovelReader.vue         # 阅读器
│   ├── NovelChapter.vue        # 章节组件
│   ├── NovelBookshelf.vue      # 书架组件
│   ├── NovelProgress.vue       # 阅读进度
│   └── NovelSettings.vue       # 阅读设置
└── world/                      # 世界组件
    ├── WorldMap.vue            # 世界地图
    ├── WorldTimeline.vue       # 时间线
    ├── WorldLorebook.vue       # 设定集
    └── WorldExplorer.vue       # 世界探索器
```

### 2. src/pages/ - 页面目录

#### 页面组织原则
- 每个页面一个目录
- 页面内包含子页面和专用组件
- 遵循 UniApp 的页面配置规范

#### 页面目录结构
```
pages/
├── home/                       # 首页
│   ├── index.vue               # 首页主页面
│   ├── chat/                   # 聊天子页面
│   │   ├── index.vue           # 角色库页面
│   │   ├── conversation.vue    # 对话页面
│   │   └── group-chat.vue      # 群聊页面
│   ├── novel/                  # 小说子页面
│   │   ├── index.vue           # 小说库页面
│   │   ├── reader.vue          # 阅读页面
│   │   └── chapter-list.vue    # 章节列表
│   ├── world/                  # 世界子页面
│   │   ├── index.vue           # 世界库页面
│   │   ├── explorer.vue        # 世界探索页面
│   │   └── lorebook.vue        # 设定页面
│   └── components/             # 首页专用组件
├── discovery/                  # 发现页
│   ├── index.vue               # 发现主页
│   ├── detail.vue              # 内容详情页
│   ├── search.vue              # 搜索页面
│   ├── category.vue            # 分类页面
│   └── components/             # 发现页专用组件
├── creation/                   # 创作页
│   ├── index.vue               # 创作主页
│   ├── character-editor.vue    # 角色编辑器
│   ├── novel-editor.vue        # 小说编辑器
│   ├── world-editor.vue        # 世界编辑器
│   └── components/             # 创作页专用组件
├── forum/                      # 论坛页
│   ├── index.vue               # 论坛主页
│   ├── server-list.vue         # 服务器列表
│   ├── channel.vue             # 频道页面
│   ├── thread.vue              # 话题页面
│   └── components/             # 论坛专用组件
└── profile/                    # 个人中心
    ├── index.vue               # 个人主页
    ├── settings.vue            # 设置页面
    ├── subscription.vue        # 订阅管理
    ├── achievements.vue        # 成就页面
    └── components/             # 个人中心专用组件
```

### 3. src/stores/ - 状态管理

#### Store 模块化设计
```
stores/
├── modules/                    # Store 模块
│   ├── app.ts                  # 应用全局状态
│   ├── user.ts                 # 用户状态
│   ├── auth.ts                 # 认证状态
│   ├── chat.ts                 # 聊天状态
│   ├── novel.ts                # 小说状态
│   ├── world.ts                # 世界状态
│   ├── creation.ts             # 创作状态
│   ├── discovery.ts            # 发现状态
│   ├── forum.ts                # 论坛状态
│   └── settings.ts             # 设置状态
├── types/                      # Store 类型定义
│   ├── app.ts
│   ├── user.ts
│   └── chat.ts
└── index.ts                    # Store 入口文件
```

#### Store 文件结构示例
```typescript
// stores/modules/chat.ts
import { defineStore } from 'pinia'
import type { ChatState, ChatMessage, Character } from '@/types/chat'

export const useChatStore = defineStore('chat', () => {
  // State
  const characters = ref<Character[]>([])
  const currentChat = ref<string | null>(null)
  const messages = ref<Map<string, ChatMessage[]>>(new Map())
  const loading = ref(false)

  // Getters
  const currentMessages = computed(() => 
    currentChat.value ? messages.value.get(currentChat.value) || [] : []
  )

  // Actions
  const sendMessage = async (content: string) => {
    // 实现逻辑
  }

  return {
    // State
    characters,
    currentChat,
    messages,
    loading,
    // Getters
    currentMessages,
    // Actions
    sendMessage
  }
})
```

### 4. src/services/ - 服务层

#### 服务层组织
```
services/
├── api/                        # API 服务
│   ├── base.ts                 # 基础 API 配置
│   ├── auth.ts                 # 认证 API
│   ├── chat.ts                 # 聊天 API
│   ├── novel.ts                # 小说 API
│   ├── world.ts                # 世界 API
│   ├── user.ts                 # 用户 API
│   └── content.ts              # 内容 API
├── ai/                         # AI 服务
│   ├── chat-service.ts         # AI 对话服务
│   ├── generation-service.ts   # 内容生成服务
│   ├── analysis-service.ts     # 内容分析服务
│   └── providers/              # AI 提供商
│       ├── openai.ts
│       ├── claude.ts
│       └── local.ts
├── storage/                    # 存储服务
│   ├── local-storage.ts        # 本地存储
│   ├── cache-service.ts        # 缓存服务
│   └── file-service.ts         # 文件服务
└── websocket/                  # WebSocket 服务
    ├── connection-manager.ts   # 连接管理
    ├── message-handler.ts      # 消息处理
    └── event-emitter.ts        # 事件发射器
```

### 5. src/types/ - 类型定义

#### 类型文件组织
```
types/
├── api.ts                      # API 相关类型
├── chat.ts                     # 聊天相关类型
├── novel.ts                    # 小说相关类型
├── world.ts                    # 世界相关类型
├── user.ts                     # 用户相关类型
├── creation.ts                 # 创作相关类型
├── forum.ts                    # 论坛相关类型
├── common.ts                   # 通用类型
└── index.ts                    # 类型导出入口
```

#### 类型定义示例
```typescript
// types/chat.ts
export interface ChatMessage {
  id: string
  content: string
  timestamp: number
  sender: 'user' | 'ai'
  characterId?: string
  metadata?: MessageMetadata
}

export interface Character {
  id: string
  name: string
  avatar: string
  description: string
  personality: string[]
  greeting: string
  examples: ChatExample[]
  createdAt: number
  updatedAt: number
}

export interface ChatSession {
  id: string
  characterId: string
  title: string
  messages: ChatMessage[]
  createdAt: number
  lastActiveAt: number
}
```

## 资源管理规范

### 1. 静态资源组织
```
assets/
├── images/                     # 图片资源
│   ├── avatars/                # 头像图片
│   ├── backgrounds/            # 背景图片
│   ├── icons/                  # 图标图片
│   └── illustrations/          # 插画图片
├── icons/                      # 矢量图标
│   ├── common/                 # 通用图标
│   ├── chat/                   # 聊天图标
│   ├── novel/                  # 小说图标
│   └── world/                  # 世界图标
├── fonts/                      # 字体文件
│   ├── primary/                # 主要字体
│   └── secondary/              # 辅助字体
└── styles/                     # 样式文件
    ├── variables.scss          # 变量定义
    ├── mixins.scss             # 混入定义
    ├── base.scss               # 基础样式
    ├── components.scss         # 组件样式
    └── utilities.scss          # 工具样式
```

### 2. 资源命名规范
```
# 图片命名
avatar-default.png             # 默认头像
bg-chat-bubble.png             # 聊天气泡背景
icon-send-message.svg          # 发送消息图标
illustration-empty-state.png   # 空状态插画

# 样式文件命名
_variables.scss                # 变量文件（下划线开头）
_mixins.scss                   # 混入文件
chat-message.scss              # 组件样式文件
```

## 配置文件管理

### 1. 环境配置
```typescript
// .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_AI_SERVICE_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3002
VITE_APP_ENV=development

// .env.production
VITE_API_BASE_URL=https://api.nextnovel.com
VITE_AI_SERVICE_URL=https://ai.nextnovel.com
VITE_WS_URL=wss://ws.nextnovel.com
VITE_APP_ENV=production
```

### 2. UniApp 配置
```json
// manifest.json
{
  "name": "NextNovel",
  "appid": "__UNI__XXXXXXX",
  "description": "AI驱动的创意内容平台",
  "versionName": "1.0.0",
  "versionCode": "100",
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3
  }
}

// pages.json
{
  "pages": [
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页"
      }
    }
  ],
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#007AFF",
    "borderStyle": "black",
    "backgroundColor": "#F8F8F8",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "iconPath": "static/tab-home.png",
        "selectedIconPath": "static/tab-home-active.png",
        "text": "首页"
      }
    ]
  }
}
```

---

*本项目结构设计遵循模块化、可维护性和可扩展性原则，为 NEXTNOVEL APP 的开发提供清晰的组织架构。*
