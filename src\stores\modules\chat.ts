import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ID, LoadingState } from '@/types'

// 聊天消息接口
export interface ChatMessage {
  id: ID
  content: string
  timestamp: number
  sender: 'user' | 'ai'
  characterId?: ID
  type: 'text' | 'image' | 'audio' | 'system'
  metadata?: {
    tokens?: number
    model?: string
    temperature?: number
    thinking?: string
    regenerated?: boolean
  }
  status: 'sending' | 'sent' | 'failed'
}

// 角色信息接口
export interface Character {
  id: ID
  name: string
  avatar: string
  description: string
  personality: string[]
  greeting: string
  examples: ChatExample[]
  settings: {
    temperature: number
    maxTokens: number
    model: string
  }
  tags: string[]
  isPublic: boolean
  createdBy: ID
  createdAt: number
  updatedAt: number
  stats: {
    chats: number
    likes: number
    downloads: number
  }
}

// 对话示例
export interface ChatExample {
  user: string
  assistant: string
}

// 聊天会话接口
export interface ChatSession {
  id: ID
  characterId: ID
  title: string
  messages: ChatMessage[]
  createdAt: number
  lastActiveAt: number
  messageCount: number
  tokenCount: number
}

// 群聊接口
export interface GroupChat {
  id: ID
  name: string
  avatar?: string
  description?: string
  characters: ID[]
  members: ID[]
  messages: ChatMessage[]
  createdBy: ID
  createdAt: number
  lastActiveAt: number
}

export const useChatStore = defineStore('chat', () => {
  // State
  const characters = ref<Character[]>([])
  const sessions = ref<Map<ID, ChatSession>>(new Map())
  const groupChats = ref<GroupChat[]>([])
  const currentSessionId = ref<ID | null>(null)
  const currentGroupChatId = ref<ID | null>(null)
  const loading = ref<LoadingState>('idle')
  const error = ref<string | null>(null)
  const isTyping = ref(false)
  const typingCharacterId = ref<ID | null>(null)

  // Getters
  const currentSession = computed(() => 
    currentSessionId.value ? sessions.value.get(currentSessionId.value) : null
  )
  
  const currentCharacter = computed(() => {
    if (!currentSession.value) return null
    return characters.value.find(char => char.id === currentSession.value!.characterId)
  })
  
  const currentMessages = computed(() => 
    currentSession.value?.messages || []
  )
  
  const currentGroupChat = computed(() => 
    groupChats.value.find(group => group.id === currentGroupChatId.value)
  )
  
  const favoriteCharacters = computed(() => 
    characters.value.filter(char => char.stats.likes > 0).slice(0, 10)
  )
  
  const recentSessions = computed(() => {
    const sessionList = Array.from(sessions.value.values())
    return sessionList
      .sort((a, b) => b.lastActiveAt - a.lastActiveAt)
      .slice(0, 20)
  })

  // Actions
  const loadCharacters = async () => {
    try {
      loading.value = 'loading'
      error.value = null

      // TODO: 调用 API 加载角色列表
      // const response = await chatApi.getCharacters()
      // characters.value = response.data
      
      // 临时数据
      characters.value = [
        {
          id: '1',
          name: '小助手',
          avatar: '/static/images/assistant.png',
          description: '我是你的AI助手，可以帮助你解答问题和进行对话。',
          personality: ['友善', '耐心', '博学'],
          greeting: '你好！我是你的AI助手，有什么可以帮助你的吗？',
          examples: [
            { user: '你好', assistant: '你好！很高兴见到你！' },
            { user: '你能做什么？', assistant: '我可以回答问题、聊天对话、协助创作等等。' }
          ],
          settings: {
            temperature: 0.7,
            maxTokens: 2000,
            model: 'gpt-3.5-turbo'
          },
          tags: ['助手', '通用'],
          isPublic: true,
          createdBy: 'system',
          createdAt: Date.now() - 86400000,
          updatedAt: Date.now() - 86400000,
          stats: {
            chats: 1000,
            likes: 500,
            downloads: 200
          }
        }
      ]
      
      loading.value = 'success'
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载角色失败'
      loading.value = 'error'
      throw err
    }
  }

  const createSession = async (characterId: ID): Promise<ChatSession> => {
    const character = characters.value.find(char => char.id === characterId)
    if (!character) {
      throw new Error('角色不存在')
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const session: ChatSession = {
      id: sessionId,
      characterId,
      title: `与${character.name}的对话`,
      messages: [
        {
          id: `msg_${Date.now()}`,
          content: character.greeting,
          timestamp: Date.now(),
          sender: 'ai',
          characterId,
          type: 'text',
          status: 'sent'
        }
      ],
      createdAt: Date.now(),
      lastActiveAt: Date.now(),
      messageCount: 1,
      tokenCount: character.greeting.length
    }

    sessions.value.set(sessionId, session)
    currentSessionId.value = sessionId
    
    return session
  }

  const sendMessage = async (content: string): Promise<ChatMessage> => {
    if (!currentSession.value || !currentCharacter.value) {
      throw new Error('没有活跃的对话会话')
    }

    try {
      // 创建用户消息
      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}_user`,
        content,
        timestamp: Date.now(),
        sender: 'user',
        type: 'text',
        status: 'sent'
      }

      // 添加到消息列表
      currentSession.value.messages.push(userMessage)
      currentSession.value.lastActiveAt = Date.now()
      currentSession.value.messageCount += 1

      // 设置AI正在输入状态
      isTyping.value = true
      typingCharacterId.value = currentCharacter.value.id

      // TODO: 调用AI API生成回复
      // const response = await aiService.chat({
      //   messages: currentSession.value.messages,
      //   character: currentCharacter.value
      // })

      // 模拟AI回复
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
      
      const aiMessage: ChatMessage = {
        id: `msg_${Date.now()}_ai`,
        content: `这是对"${content}"的回复。我理解你的意思，让我们继续对话吧！`,
        timestamp: Date.now(),
        sender: 'ai',
        characterId: currentCharacter.value.id,
        type: 'text',
        status: 'sent',
        metadata: {
          tokens: 50,
          model: currentCharacter.value.settings.model,
          temperature: currentCharacter.value.settings.temperature
        }
      }

      // 添加AI回复
      currentSession.value.messages.push(aiMessage)
      currentSession.value.lastActiveAt = Date.now()
      currentSession.value.messageCount += 1
      currentSession.value.tokenCount += aiMessage.metadata?.tokens || 0

      return aiMessage
    } catch (err) {
      error.value = err instanceof Error ? err.message : '发送消息失败'
      throw err
    } finally {
      isTyping.value = false
      typingCharacterId.value = null
    }
  }

  const regenerateMessage = async (messageId: ID): Promise<ChatMessage> => {
    if (!currentSession.value || !currentCharacter.value) {
      throw new Error('没有活跃的对话会话')
    }

    const messageIndex = currentSession.value.messages.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1 || currentSession.value.messages[messageIndex].sender !== 'ai') {
      throw new Error('无法重新生成该消息')
    }

    try {
      isTyping.value = true
      typingCharacterId.value = currentCharacter.value.id

      // TODO: 调用AI API重新生成
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
      
      const newMessage: ChatMessage = {
        ...currentSession.value.messages[messageIndex],
        id: `msg_${Date.now()}_ai_regen`,
        content: `这是重新生成的回复：${Math.random().toString(36).substr(2, 20)}`,
        timestamp: Date.now(),
        metadata: {
          ...currentSession.value.messages[messageIndex].metadata,
          regenerated: true
        }
      }

      // 替换原消息
      currentSession.value.messages[messageIndex] = newMessage
      currentSession.value.lastActiveAt = Date.now()

      return newMessage
    } catch (err) {
      error.value = err instanceof Error ? err.message : '重新生成失败'
      throw err
    } finally {
      isTyping.value = false
      typingCharacterId.value = null
    }
  }

  const deleteMessage = (messageId: ID) => {
    if (!currentSession.value) return

    const messageIndex = currentSession.value.messages.findIndex(msg => msg.id === messageId)
    if (messageIndex !== -1) {
      currentSession.value.messages.splice(messageIndex, 1)
      currentSession.value.messageCount -= 1
      currentSession.value.lastActiveAt = Date.now()
    }
  }

  const deleteSession = (sessionId: ID) => {
    sessions.value.delete(sessionId)
    if (currentSessionId.value === sessionId) {
      currentSessionId.value = null
    }
  }

  const setCurrentSession = (sessionId: ID | null) => {
    currentSessionId.value = sessionId
  }

  const updateSessionTitle = (sessionId: ID, title: string) => {
    const session = sessions.value.get(sessionId)
    if (session) {
      session.title = title
      session.lastActiveAt = Date.now()
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearAllSessions = () => {
    sessions.value.clear()
    currentSessionId.value = null
  }

  return {
    // State
    characters,
    sessions,
    groupChats,
    currentSessionId,
    currentGroupChatId,
    loading,
    error,
    isTyping,
    typingCharacterId,
    
    // Getters
    currentSession,
    currentCharacter,
    currentMessages,
    currentGroupChat,
    favoriteCharacters,
    recentSessions,
    
    // Actions
    loadCharacters,
    createSession,
    sendMessage,
    regenerateMessage,
    deleteMessage,
    deleteSession,
    setCurrentSession,
    updateSessionTitle,
    clearError,
    clearAllSessions
  }
}, {
  persist: {
    key: 'chat-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value)
    },
    paths: ['sessions', 'currentSessionId']
  }
})
