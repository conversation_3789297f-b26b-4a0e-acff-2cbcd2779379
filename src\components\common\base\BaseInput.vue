<template>
  <view :class="wrapperClasses">
    <!-- 标签 -->
    <view v-if="label" class="base-input__label">
      <text class="label-text">{{ label }}</text>
      <text v-if="required" class="label-required">*</text>
    </view>
    
    <!-- 输入框容器 -->
    <view :class="inputWrapperClasses">
      <!-- 前缀图标 -->
      <view v-if="prefixIcon" class="base-input__prefix">
        <image v-if="typeof prefixIcon === 'string'" :src="prefixIcon" class="icon-image" />
        <text v-else class="icon-text">{{ prefixIcon }}</text>
      </view>
      
      <!-- 输入框 -->
      <input
        v-if="type !== 'textarea'"
        :class="inputClasses"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />
      
      <textarea
        v-else
        :class="inputClasses"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :auto-height="autoHeight"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 后缀图标 -->
      <view v-if="suffixIcon || showClear || showPassword" class="base-input__suffix">
        <!-- 清除按钮 -->
        <view
          v-if="showClear && modelValue && !disabled && !readonly"
          class="suffix-icon clear-icon"
          @click="handleClear"
        >
          <text>×</text>
        </view>
        
        <!-- 密码显示切换 -->
        <view
          v-if="showPassword && type === 'password'"
          class="suffix-icon password-icon"
          @click="togglePasswordVisible"
        >
          <text>{{ passwordVisible ? '👁️' : '👁️‍🗨️' }}</text>
        </view>
        
        <!-- 自定义后缀图标 -->
        <view v-if="suffixIcon" class="suffix-icon">
          <image v-if="typeof suffixIcon === 'string'" :src="suffixIcon" class="icon-image" />
          <text v-else class="icon-text">{{ suffixIcon }}</text>
        </view>
      </view>
    </view>
    
    <!-- 字数统计 -->
    <view v-if="showCount && maxlength" class="base-input__count">
      <text class="count-text">{{ modelValue?.length || 0 }}/{{ maxlength }}</text>
    </view>
    
    <!-- 错误信息 -->
    <view v-if="error" class="base-input__error">
      <text class="error-text">{{ error }}</text>
    </view>
    
    <!-- 帮助信息 -->
    <view v-if="help && !error" class="base-input__help">
      <text class="help-text">{{ help }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  // 输入框类型
  type?: 'text' | 'password' | 'number' | 'email' | 'tel' | 'textarea'
  // 双向绑定值
  modelValue?: string | number
  // 标签
  label?: string
  // 占位符
  placeholder?: string
  // 是否必填
  required?: boolean
  // 是否禁用
  disabled?: boolean
  // 是否只读
  readonly?: boolean
  // 最大长度
  maxlength?: number
  // 是否显示字数统计
  showCount?: boolean
  // 是否显示清除按钮
  showClear?: boolean
  // 是否显示密码切换按钮
  showPassword?: boolean
  // 前缀图标
  prefixIcon?: string
  // 后缀图标
  suffixIcon?: string
  // 错误信息
  error?: string
  // 帮助信息
  help?: string
  // 尺寸
  size?: 'small' | 'medium' | 'large'
  // textarea 自动高度
  autoHeight?: boolean
}

interface Emits {
  'update:modelValue': [value: string | number]
  focus: [event: Event]
  blur: [event: Event]
  input: [event: Event]
  confirm: [event: Event]
  clear: []
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'medium',
  showClear: false,
  showPassword: false,
  showCount: false,
  autoHeight: false
})

const emit = defineEmits<Emits>()

// 内部状态
const focused = ref(false)
const passwordVisible = ref(false)

// 计算属性
const inputType = computed(() => {
  if (props.type === 'password') {
    return passwordVisible.value ? 'text' : 'password'
  }
  return props.type === 'textarea' ? 'text' : props.type
})

const wrapperClasses = computed(() => [
  'base-input',
  `base-input--${props.size}`,
  {
    'base-input--focused': focused.value,
    'base-input--disabled': props.disabled,
    'base-input--readonly': props.readonly,
    'base-input--error': props.error
  }
])

const inputWrapperClasses = computed(() => [
  'base-input__wrapper',
  {
    'has-prefix': props.prefixIcon,
    'has-suffix': props.suffixIcon || props.showClear || props.showPassword
  }
])

const inputClasses = computed(() => [
  'base-input__control',
  {
    'is-textarea': props.type === 'textarea'
  }
])

// 事件处理
const handleInput = (event: any) => {
  const value = event.detail?.value || event.target?.value || ''
  emit('update:modelValue', value)
  emit('input', event)
}

const handleFocus = (event: Event) => {
  focused.value = true
  emit('focus', event)
}

const handleBlur = (event: Event) => {
  focused.value = false
  emit('blur', event)
}

const handleConfirm = (event: Event) => {
  emit('confirm', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

const togglePasswordVisible = () => {
  passwordVisible.value = !passwordVisible.value
}
</script>

<style lang="scss" scoped>
.base-input {
  width: 100%;
  
  // 标签样式
  &__label {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    
    .label-text {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
    }
    
    .label-required {
      color: var(--error-color);
      margin-left: 2px;
    }
  }
  
  // 输入框容器
  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: var(--bg-primary);
    border: 1px solid var(--separator);
    border-radius: var(--border-radius-md);
    transition: all 0.2s ease;
    
    &.has-prefix {
      .base-input__control {
        padding-left: 40px;
      }
    }
    
    &.has-suffix {
      .base-input__control {
        padding-right: 40px;
      }
    }
  }
  
  // 输入框控件
  &__control {
    @include input-base;
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    
    &.is-textarea {
      min-height: 80px;
      resize: vertical;
      line-height: var(--line-height-normal);
    }
  }
  
  // 前缀和后缀
  &__prefix,
  &__suffix {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: var(--text-tertiary);
    
    .icon-image {
      width: 16px;
      height: 16px;
    }
    
    .icon-text {
      font-size: 16px;
    }
  }
  
  &__prefix {
    left: var(--spacing-xs);
  }
  
  &__suffix {
    right: var(--spacing-xs);
    gap: var(--spacing-xs);
    
    .suffix-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      cursor: pointer;
      border-radius: var(--border-radius-sm);
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: var(--bg-secondary);
      }
      
      &.clear-icon {
        font-size: 18px;
        font-weight: bold;
      }
      
      &.password-icon {
        font-size: 14px;
      }
    }
  }
  
  // 字数统计
  &__count {
    margin-top: var(--spacing-xs);
    text-align: right;
    
    .count-text {
      font-size: var(--font-size-xs);
      color: var(--text-tertiary);
    }
  }
  
  // 错误信息
  &__error {
    margin-top: var(--spacing-xs);
    
    .error-text {
      font-size: var(--font-size-xs);
      color: var(--error-color);
    }
  }
  
  // 帮助信息
  &__help {
    margin-top: var(--spacing-xs);
    
    .help-text {
      font-size: var(--font-size-xs);
      color: var(--text-tertiary);
    }
  }
  
  // 尺寸变体
  &--small {
    .base-input__wrapper {
      min-height: 32px;
    }
    
    .base-input__control {
      padding: 6px 12px;
      font-size: var(--font-size-sm);
    }
  }
  
  &--medium {
    .base-input__wrapper {
      min-height: 40px;
    }
    
    .base-input__control {
      padding: 8px 16px;
      font-size: var(--font-size-md);
    }
  }
  
  &--large {
    .base-input__wrapper {
      min-height: 48px;
    }
    
    .base-input__control {
      padding: 12px 20px;
      font-size: var(--font-size-lg);
    }
  }
  
  // 状态样式
  &--focused {
    .base-input__wrapper {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
    }
  }
  
  &--disabled {
    .base-input__wrapper {
      background-color: var(--bg-secondary);
      cursor: not-allowed;
    }
    
    .base-input__control {
      color: var(--text-tertiary);
      cursor: not-allowed;
    }
  }
  
  &--readonly {
    .base-input__wrapper {
      background-color: var(--bg-secondary);
    }
  }
  
  &--error {
    .base-input__wrapper {
      border-color: var(--error-color);
    }
  }
}
</style>
