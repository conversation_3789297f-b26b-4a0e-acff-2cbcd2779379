// NextNovel App Mixins

// 响应式断点
$breakpoints: (
  'small': 320px,
  'medium': 768px,
  'large': 1024px,
  'xlarge': 1440px
);

// 响应式 mixin
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// Flexbox 布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 按钮样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: var(--primary-color);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: var(--primary-dark);
  }
  
  &:active:not(:disabled) {
    transform: scale(0.98);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--separator);
  
  &:hover:not(:disabled) {
    background-color: var(--bg-tertiary);
  }
}

// 卡片样式
@mixin card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

@mixin card-hover {
  @include card;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
  }
}

// 输入框样式
@mixin input-base {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--separator);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  transition: border-color 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
  
  &::placeholder {
    color: var(--text-tertiary);
  }
  
  &:disabled {
    background-color: var(--bg-secondary);
    color: var(--text-tertiary);
    cursor: not-allowed;
  }
}

// 滚动条样式
@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--text-quaternary);
    border-radius: 3px;
    
    &:hover {
      background: var(--text-tertiary);
    }
  }
}

// 动画
@mixin fade-in($duration: 0.3s) {
  animation: fadeIn $duration ease-in-out;
}

@mixin slide-up($duration: 0.3s) {
  animation: slideUp $duration ease-out;
}

@mixin bounce-in($duration: 0.5s) {
  animation: bounceIn $duration ease-out;
}

// 关键帧动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 工具类生成器
@mixin generate-spacing($property, $prefix) {
  @each $name, $value in (
    'xs': var(--spacing-xs),
    'sm': var(--spacing-sm),
    'md': var(--spacing-md),
    'lg': var(--spacing-lg),
    'xl': var(--spacing-xl),
    '2xl': var(--spacing-2xl)
  ) {
    .#{$prefix}-#{$name} {
      #{$property}: #{$value};
    }
  }
}
