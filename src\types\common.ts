// 通用类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: string
  timestamp: number
}

// 错误类型
export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: number
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 排序参数
export interface SortParams {
  field: string
  order: 'asc' | 'desc'
}

// 筛选参数
export interface FilterParams {
  [key: string]: any
}

// 搜索参数
export interface SearchParams {
  query: string
  filters?: FilterParams
  sort?: SortParams
  pagination?: PaginationParams
}

// 加载状态
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// 内容类型
export type ContentType = 'character' | 'novel' | 'world' | 'forum_post'

// 文件类型
export interface FileInfo {
  id: string
  name: string
  size: number
  type: string
  url: string
  createdAt: number
}

// 标签类型
export interface Tag {
  id: string
  name: string
  color?: string
  category?: string
}

// 评分类型
export interface Rating {
  score: number
  count: number
  distribution: {
    [key: number]: number
  }
}

// 统计数据
export interface Statistics {
  views: number
  likes: number
  shares: number
  comments: number
  downloads: number
}

// 时间戳相关
export type Timestamp = number

// ID 类型
export type ID = string

// 状态类型
export type Status = 'active' | 'inactive' | 'pending' | 'deleted'

// 可见性类型
export type Visibility = 'public' | 'private' | 'unlisted'

// 设备信息
export interface DeviceInfo {
  platform: string
  version: string
  model: string
  brand: string
}

// 位置信息
export interface LocationInfo {
  latitude: number
  longitude: number
  address?: string
  city?: string
  country?: string
}

// 通知类型
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: number
  read: boolean
  data?: any
}

// 键值对
export interface KeyValuePair<T = any> {
  key: string
  value: T
}

// 选项类型
export interface Option<T = any> {
  label: string
  value: T
  disabled?: boolean
  icon?: string
}

// 菜单项
export interface MenuItem {
  id: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  disabled?: boolean
  badge?: string | number
}

// 表单字段
export interface FormField {
  name: string
  label: string
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date'
  required?: boolean
  placeholder?: string
  options?: Option[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

// 主题配置
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto'
  primaryColor: string
  accentColor: string
  fontSize: 'small' | 'medium' | 'large'
}

// 语言配置
export interface LanguageConfig {
  code: string
  name: string
  flag?: string
}

// 应用配置
export interface AppConfig {
  theme: ThemeConfig
  language: LanguageConfig
  notifications: boolean
  autoSave: boolean
  debugMode: boolean
}
