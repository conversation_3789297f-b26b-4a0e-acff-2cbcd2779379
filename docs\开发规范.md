# NEXTNOVEL APP 开发规范和编码标准

## 代码规范

### 1. TypeScript 编码规范

#### 基本原则
- 使用严格模式：`"strict": true`
- 优先使用类型推导，避免显式 `any` 类型
- 使用接口定义对象结构，使用类型别名定义联合类型

#### 类型定义
```typescript
// ✅ 推荐：使用接口定义对象结构
interface ChatMessage {
  id: string
  content: string
  timestamp: number
  sender: 'user' | 'ai'
  metadata?: MessageMetadata
}

// ✅ 推荐：使用类型别名定义联合类型
type ContentType = 'character' | 'novel' | 'world'
type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// ❌ 避免：使用 any 类型
const data: any = response.data // 不推荐

// ✅ 推荐：使用具体类型
const data: ApiResponse<ChatMessage[]> = response.data
```

#### 函数定义
```typescript
// ✅ 推荐：使用箭头函数和明确的返回类型
const sendMessage = async (content: string): Promise<ChatResponse> => {
  // 实现逻辑
}

// ✅ 推荐：使用泛型提高复用性
const createApiClient = <T>(baseURL: string): ApiClient<T> => {
  // 实现逻辑
}
```

### 2. Vue 3 组件规范

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { ChatMessage } from '@/types'

// 2. 定义 Props 和 Emits
interface Props {
  messages: ChatMessage[]
  loading?: boolean
}

interface Emits {
  send: [content: string]
  delete: [messageId: string]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 3. 响应式数据
const inputValue = ref('')
const isComposing = ref(false)

// 4. 计算属性
const canSend = computed(() => 
  inputValue.value.trim().length > 0 && !props.loading
)

// 5. 方法定义
const handleSend = () => {
  if (canSend.value) {
    emit('send', inputValue.value.trim())
    inputValue.value = ''
  }
}

// 6. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 样式定义 */
</style>
```

#### 组件命名
```typescript
// ✅ 推荐：使用 PascalCase
export default defineComponent({
  name: 'ChatMessageList'
})

// ✅ 推荐：文件名使用 kebab-case
// chat-message-list.vue
// user-profile-card.vue
```

### 3. CSS/SCSS 规范

#### 命名约定
```scss
// ✅ 推荐：使用 BEM 命名方法
.chat-message {
  &__content {
    padding: 12px;
  }
  
  &__timestamp {
    font-size: 12px;
    color: #999;
  }
  
  &--sent {
    text-align: right;
  }
  
  &--received {
    text-align: left;
  }
}

// ✅ 推荐：使用 CSS 变量
:root {
  --primary-color: #007AFF;
  --secondary-color: #5856D6;
  --background-color: #F2F2F7;
  --text-color: #000000;
  --border-radius: 8px;
  --spacing-unit: 4px;
}
```

#### 响应式设计
```scss
// ✅ 推荐：使用统一的断点
$breakpoints: (
  'small': 320px,
  'medium': 768px,
  'large': 1024px,
  'xlarge': 1440px
);

@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}

.chat-container {
  padding: calc(var(--spacing-unit) * 2);
  
  @include respond-to('medium') {
    padding: calc(var(--spacing-unit) * 4);
  }
}
```

## 命名约定

### 1. 文件和目录命名
```
src/
├── components/
│   ├── common/           # 通用组件
│   │   ├── base-button.vue
│   │   └── loading-spinner.vue
│   ├── chat/            # 聊天相关组件
│   │   ├── chat-message.vue
│   │   └── chat-input.vue
│   └── novel/           # 小说相关组件
├── pages/
│   ├── home/
│   │   ├── index.vue
│   │   └── components/
│   └── discovery/
├── stores/
│   ├── user.ts
│   └── chat.ts
├── services/
│   ├── api/
│   └── ai/
└── utils/
    ├── format.ts
    └── validation.ts
```

### 2. 变量和函数命名
```typescript
// ✅ 推荐：使用 camelCase
const userName = 'John Doe'
const isLoading = false
const messageCount = 10

// ✅ 推荐：函数名使用动词开头
const getUserInfo = () => {}
const validateInput = () => {}
const handleSubmit = () => {}

// ✅ 推荐：常量使用 UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'
const MAX_MESSAGE_LENGTH = 1000

// ✅ 推荐：类型名使用 PascalCase
interface UserProfile {}
type MessageStatus = 'sent' | 'delivered' | 'read'
```

### 3. 组件和页面命名
```typescript
// ✅ 推荐：组件名使用 PascalCase
const ChatMessage = defineComponent({})
const UserProfileCard = defineComponent({})

// ✅ 推荐：页面组件名包含 Page 后缀
const HomePage = defineComponent({})
const DiscoveryPage = defineComponent({})
```

## 组件设计原则

### 1. 单一职责原则
```vue
<!-- ✅ 推荐：职责单一的组件 -->
<template>
  <div class="message-item">
    <MessageAvatar :user="message.sender" />
    <MessageContent :content="message.content" />
    <MessageTimestamp :timestamp="message.timestamp" />
  </div>
</template>

<!-- ❌ 避免：职责过多的组件 -->
<template>
  <div class="chat-page">
    <!-- 包含了聊天列表、输入框、用户信息等多个职责 -->
  </div>
</template>
```

### 2. 组件通信规范
```typescript
// ✅ 推荐：使用 Props 向下传递数据
interface Props {
  message: ChatMessage
  readonly?: boolean
}

// ✅ 推荐：使用 Emits 向上传递事件
interface Emits {
  edit: [messageId: string]
  delete: [messageId: string]
  reply: [message: ChatMessage]
}

// ✅ 推荐：使用 Provide/Inject 跨层级通信
const chatContextKey = Symbol('chatContext')

// 父组件
provide(chatContextKey, {
  currentUser,
  sendMessage
})

// 子组件
const chatContext = inject(chatContextKey)
```

### 3. 组件复用性
```typescript
// ✅ 推荐：使用泛型提高复用性
interface ListProps<T> {
  items: T[]
  keyField: keyof T
  renderItem: (item: T) => VNode
}

// ✅ 推荐：使用插槽提供灵活性
<template>
  <div class="card">
    <header class="card__header">
      <slot name="header" />
    </header>
    <main class="card__content">
      <slot />
    </main>
    <footer class="card__footer">
      <slot name="footer" />
    </footer>
  </div>
</template>
```

## API 设计规范

### 1. RESTful API 设计
```typescript
// ✅ 推荐：RESTful 风格的 API 设计
const chatApi = {
  // GET /api/chats - 获取聊天列表
  getChats: (): Promise<Chat[]> => {},
  
  // GET /api/chats/:id - 获取特定聊天
  getChat: (id: string): Promise<Chat> => {},
  
  // POST /api/chats - 创建新聊天
  createChat: (data: CreateChatRequest): Promise<Chat> => {},
  
  // PUT /api/chats/:id - 更新聊天
  updateChat: (id: string, data: UpdateChatRequest): Promise<Chat> => {},
  
  // DELETE /api/chats/:id - 删除聊天
  deleteChat: (id: string): Promise<void> => {}
}
```

### 2. 请求响应格式
```typescript
// ✅ 推荐：统一的响应格式
interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  code?: string
  timestamp: number
}

// ✅ 推荐：统一的错误格式
interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: number
}
```

### 3. 请求拦截器
```typescript
// ✅ 推荐：统一的请求处理
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证头
    const token = getAuthToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    // 统一错误处理
    handleApiError(error)
    return Promise.reject(error)
  }
)
```

## 错误处理规范

### 1. 错误分类
```typescript
// ✅ 推荐：错误类型定义
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

interface AppError {
  type: ErrorType
  message: string
  code?: string
  details?: any
}
```

### 2. 错误处理策略
```typescript
// ✅ 推荐：统一的错误处理
const handleError = (error: AppError) => {
  switch (error.type) {
    case ErrorType.NETWORK_ERROR:
      showToast('网络连接异常，请检查网络设置')
      break
    case ErrorType.AUTHENTICATION_ERROR:
      redirectToLogin()
      break
    case ErrorType.VALIDATION_ERROR:
      showValidationErrors(error.details)
      break
    default:
      showToast(error.message || '操作失败，请稍后重试')
  }
}
```

## 性能优化规范

### 1. 组件优化
```typescript
// ✅ 推荐：使用 defineAsyncComponent 懒加载
const ChatPage = defineAsyncComponent(() => import('@/pages/chat/index.vue'))

// ✅ 推荐：使用 shallowRef 优化大对象
const largeData = shallowRef<LargeDataType>({})

// ✅ 推荐：使用 computed 缓存计算结果
const filteredMessages = computed(() => 
  messages.value.filter(msg => msg.content.includes(searchQuery.value))
)
```

### 2. 列表优化
```vue
<template>
  <!-- ✅ 推荐：使用 key 优化列表渲染 -->
  <div
    v-for="message in messages"
    :key="message.id"
    class="message-item"
  >
    {{ message.content }}
  </div>
  
  <!-- ✅ 推荐：使用虚拟滚动处理长列表 -->
  <VirtualList
    :items="messages"
    :item-height="60"
    :visible-count="10"
  >
    <template #item="{ item }">
      <MessageItem :message="item" />
    </template>
  </VirtualList>
</template>
```

## 测试规范

### 1. 单元测试
```typescript
// ✅ 推荐：测试文件命名
// utils/format.ts -> utils/__tests__/format.test.ts
// components/ChatMessage.vue -> components/__tests__/ChatMessage.test.ts

describe('formatMessage', () => {
  it('should format message correctly', () => {
    const message = { content: 'Hello', timestamp: 1234567890 }
    const result = formatMessage(message)
    expect(result).toContain('Hello')
  })
})
```

### 2. 组件测试
```typescript
import { mount } from '@vue/test-utils'
import ChatMessage from '@/components/ChatMessage.vue'

describe('ChatMessage', () => {
  it('should render message content', () => {
    const wrapper = mount(ChatMessage, {
      props: {
        message: {
          id: '1',
          content: 'Test message',
          sender: 'user'
        }
      }
    })
    
    expect(wrapper.text()).toContain('Test message')
  })
})
```

---

*本开发规范基于现代前端开发最佳实践，结合 NEXTNOVEL APP 的技术栈特点制定，旨在提高代码质量和开发效率。*
