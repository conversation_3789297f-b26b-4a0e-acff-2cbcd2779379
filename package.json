{"name": "nextnovel-app", "version": "1.0.0", "description": "AI驱动的创意内容移动应用", "main": "main.ts", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-app-plus": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-alipay": "^3.0.0", "@dcloudio/uni-mp-baidu": "^3.0.0", "@dcloudio/uni-mp-kuaishou": "^3.0.0", "@dcloudio/uni-mp-lark": "^3.0.0", "@dcloudio/uni-mp-qq": "^3.0.0", "@dcloudio/uni-mp-toutiao": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-mp-xhs": "^3.0.0", "@dcloudio/uni-quickapp-webview": "^3.0.0", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "axios": "^1.6.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "crypto-js": "^4.2.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "^3.0.0", "@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "@types/crypto-js": "^4.2.1", "@types/lodash-es": "^4.17.12", "@types/node": "^18.15.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.5", "typescript": "~5.0.0", "vite": "4.4.9", "vue-tsc": "^1.8.25"}, "keywords": ["uniapp", "vue3", "typescript", "ai", "mobile", "chat", "novel", "creative"], "author": "NextNovel Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}