{"name": "nextnovel-app", "version": "1.0.0", "description": "AI驱动的创意内容移动应用", "main": "main.ts", "scripts": {"dev": "uni", "dev:h5": "uni --platform h5", "dev:mp-weixin": "uni --platform mp-weixin", "dev:app": "uni --platform app", "build": "uni build", "build:h5": "uni build --platform h5", "build:mp-weixin": "uni build --platform mp-weixin", "build:app": "uni build --platform app", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:style": "stylelint **/*.{vue,css,scss} --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "preview": "vite preview"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-4010920240507001", "@dcloudio/uni-components": "^3.0.0-4010920240507001", "@dcloudio/uni-h5": "^3.0.0-4010920240507001", "@dcloudio/uni-mp-weixin": "^3.0.0-4010920240507001", "@dcloudio/uni-app-plus": "^3.0.0-4010920240507001", "vue": "^3.4.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "axios": "^1.6.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "crypto-js": "^4.2.0", "socket.io-client": "^4.7.4"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "^3.0.0-4010920240507001", "@dcloudio/uni-cli-shared": "^3.0.0-4010920240507001", "@dcloudio/vite-plugin-uni": "^3.0.0-4010920240507001", "@types/crypto-js": "^4.2.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "prettier": "^3.1.1", "sass": "^1.69.5", "stylelint": "^16.0.2", "stylelint-config-standard-scss": "^12.0.0", "stylelint-config-standard-vue": "^1.0.0", "typescript": "^5.3.3", "vite": "^5.0.10", "vitest": "^1.0.4", "vue-tsc": "^1.8.25"}, "keywords": ["uniapp", "vue3", "typescript", "ai", "mobile", "chat", "novel", "creative"], "author": "NextNovel Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}