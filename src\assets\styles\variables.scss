// NextNovel App 设计系统变量

// 主题色彩
:root {
  // 主色调
  --primary-color: #007AFF;
  --primary-light: #5AC8FA;
  --primary-dark: #0051D5;
  
  // 辅助色
  --secondary-color: #5856D6;
  --accent-color: #FF9500;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --error-color: #FF3B30;
  
  // 中性色
  --text-primary: #000000;
  --text-secondary: #3C3C43;
  --text-tertiary: #8E8E93;
  --text-quaternary: #C7C7CC;
  
  // 背景色
  --bg-primary: #FFFFFF;
  --bg-secondary: #F2F2F7;
  --bg-tertiary: #FFFFFF;
  --bg-grouped: #F2F2F7;
  
  // 分割线
  --separator: #C6C6C8;
  --separator-opaque: #3C3C43;
  
  // 阴影
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
  
  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 50%;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  
  // 字体权重
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  // 行高
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  // Z-index
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// 暗色主题
@media (prefers-color-scheme: dark) {
  :root {
    // 文字色
    --text-primary: #FFFFFF;
    --text-secondary: #EBEBF5;
    --text-tertiary: #EBEBF5;
    --text-quaternary: #48484A;
    
    // 背景色
    --bg-primary: #000000;
    --bg-secondary: #1C1C1E;
    --bg-tertiary: #2C2C2E;
    --bg-grouped: #000000;
    
    // 分割线
    --separator: #38383A;
    --separator-opaque: #545458;
  }
}

// SCSS 变量（兼容性）
$primary-color: var(--primary-color);
$secondary-color: var(--secondary-color);
$accent-color: var(--accent-color);
$success-color: var(--success-color);
$warning-color: var(--warning-color);
$error-color: var(--error-color);

$text-primary: var(--text-primary);
$text-secondary: var(--text-secondary);
$text-tertiary: var(--text-tertiary);

$bg-primary: var(--bg-primary);
$bg-secondary: var(--bg-secondary);
$bg-tertiary: var(--bg-tertiary);

$border-radius-sm: var(--border-radius-sm);
$border-radius-md: var(--border-radius-md);
$border-radius-lg: var(--border-radius-lg);

$spacing-xs: var(--spacing-xs);
$spacing-sm: var(--spacing-sm);
$spacing-md: var(--spacing-md);
$spacing-lg: var(--spacing-lg);
$spacing-xl: var(--spacing-xl);
