<template>
  <view v-if="visible" :class="modalClasses" @click="handleMaskClick">
    <view :class="dialogClasses" @click.stop>
      <!-- 头部 -->
      <view v-if="showHeader" class="base-modal__header">
        <view class="header-title">
          <slot name="title">
            <text v-if="title">{{ title }}</text>
          </slot>
        </view>
        
        <view v-if="showClose" class="header-close" @click="handleClose">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <!-- 内容 -->
      <view class="base-modal__body">
        <slot />
      </view>
      
      <!-- 底部 -->
      <view v-if="showFooter" class="base-modal__footer">
        <slot name="footer">
          <BaseButton
            v-if="showCancel"
            type="secondary"
            @click="handleCancel"
          >
            {{ cancelText }}
          </BaseButton>
          <BaseButton
            v-if="showConfirm"
            type="primary"
            :loading="confirmLoading"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </BaseButton>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, watch, nextTick } from 'vue'
import BaseButton from './BaseButton.vue'

interface Props {
  // 是否显示
  visible?: boolean
  // 标题
  title?: string
  // 宽度
  width?: string | number
  // 是否显示头部
  showHeader?: boolean
  // 是否显示关闭按钮
  showClose?: boolean
  // 是否显示底部
  showFooter?: boolean
  // 是否显示取消按钮
  showCancel?: boolean
  // 是否显示确认按钮
  showConfirm?: boolean
  // 取消按钮文本
  cancelText?: string
  // 确认按钮文本
  confirmText?: string
  // 确认按钮加载状态
  confirmLoading?: boolean
  // 点击遮罩是否关闭
  maskClosable?: boolean
  // 是否居中显示
  centered?: boolean
  // 层级
  zIndex?: number
}

interface Emits {
  'update:visible': [visible: boolean]
  close: []
  cancel: []
  confirm: []
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  showHeader: true,
  showClose: true,
  showFooter: true,
  showCancel: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确定',
  confirmLoading: false,
  maskClosable: true,
  centered: true,
  zIndex: 1000
})

const emit = defineEmits<Emits>()

// 计算样式类
const modalClasses = computed(() => [
  'base-modal',
  {
    'base-modal--centered': props.centered
  }
])

const dialogClasses = computed(() => [
  'base-modal__dialog',
  {
    'base-modal__dialog--no-header': !props.showHeader,
    'base-modal__dialog--no-footer': !props.showFooter
  }
])

// 事件处理
const handleMaskClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

const handleConfirm = () => {
  emit('confirm')
}

// 监听显示状态变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 显示时禁止页面滚动
    nextTick(() => {
      document.body.style.overflow = 'hidden'
    })
  } else {
    // 隐藏时恢复页面滚动
    document.body.style.overflow = ''
  }
})
</script>

<style lang="scss" scoped>
.base-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-mask);
  z-index: v-bind(zIndex);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--spacing-lg);
  overflow-y: auto;
  animation: fadeIn 0.3s ease-out;
  
  &--centered {
    align-items: center;
  }
  
  // 对话框容器
  &__dialog {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-heavy);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease-out;
    
    &--no-header {
      .base-modal__body {
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
      }
    }
    
    &--no-footer {
      .base-modal__body {
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
      }
    }
  }
  
  // 头部
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--separator);
    
    .header-title {
      flex: 1;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
    }
    
    .header-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: var(--border-radius-sm);
      cursor: pointer;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: var(--bg-secondary);
      }
      
      .close-icon {
        font-size: 24px;
        font-weight: bold;
        color: var(--text-tertiary);
      }
    }
  }
  
  // 内容
  &__body {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    color: var(--text-primary);
    line-height: var(--line-height-normal);
  }
  
  // 底部
  &__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--separator);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式适配
@include respond-to('small') {
  .base-modal {
    padding: var(--spacing-sm);
    
    &__dialog {
      max-width: 100%;
    }
    
    &__header,
    &__body,
    &__footer {
      padding: var(--spacing-md);
    }
  }
}
</style>
