<template>
  <view class="world-panel">
    <scroll-view class="world-list" scroll-y>
      <!-- 搜索和筛选 -->
      <view class="search-section">
        <BaseInput
          v-model="searchQuery"
          placeholder="搜索世界..."
          prefix-icon="🔍"
          show-clear
        />
        
        <view class="category-tabs">
          <view
            v-for="category in categories"
            :key="category.key"
            :class="['category-tab', { active: currentCategory === category.key }]"
            @click="setCategory(category.key)"
          >
            <text class="category-icon">{{ category.icon }}</text>
            <text class="category-text">{{ category.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 世界卡片列表 -->
      <view class="world-grid">
        <view
          v-for="world in filteredWorlds"
          :key="world.id"
          class="world-card"
          @click="handleEnterWorld(world)"
        >
          <view class="card-header">
            <image :src="world.banner" class="world-banner" />
            <view class="world-overlay">
              <view class="world-title-section">
                <text class="world-title">{{ world.name }}</text>
                <text class="world-subtitle">{{ world.subtitle }}</text>
              </view>
              <view class="world-status">
                <view :class="['status-indicator', world.status]" />
                <text class="status-text">{{ getStatusText(world.status) }}</text>
              </view>
            </view>
          </view>
          
          <view class="card-content">
            <text class="world-description">{{ world.description }}</text>
            
            <view class="world-tags">
              <text
                v-for="tag in world.tags.slice(0, 3)"
                :key="tag"
                class="world-tag"
              >
                {{ tag }}
              </text>
            </view>
            
            <view class="world-stats">
              <view class="stat-group">
                <text class="stat-label">探索进度</text>
                <view class="progress-bar">
                  <view 
                    class="progress-fill" 
                    :style="{ width: `${world.explorationProgress}%` }"
                  />
                </view>
                <text class="stat-value">{{ world.explorationProgress }}%</text>
              </view>
              
              <view class="stat-row">
                <view class="stat-item">
                  <text class="stat-icon">🏛️</text>
                  <text class="stat-text">{{ world.locations }}个地点</text>
                </view>
                <view class="stat-item">
                  <text class="stat-icon">👥</text>
                  <text class="stat-text">{{ world.characters }}个角色</text>
                </view>
                <view class="stat-item">
                  <text class="stat-icon">📖</text>
                  <text class="stat-text">{{ world.stories }}个故事</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="card-actions">
            <BaseButton
              type="primary"
              size="small"
              @click.stop="handleEnterWorld(world)"
            >
              进入世界
            </BaseButton>
            <view class="action-buttons">
              <view class="action-btn" @click.stop="handleFavorite(world)">
                <text class="action-icon">{{ world.favorited ? '❤️' : '🤍' }}</text>
              </view>
              <view class="action-btn" @click.stop="handleShare(world)">
                <text class="action-icon">📤</text>
              </view>
              <view class="action-btn" @click.stop="handleViewDetails(world)">
                <text class="action-icon">ℹ️</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="filteredWorlds.length === 0" class="empty-state">
        <text class="empty-icon">🌍</text>
        <text class="empty-title">暂无世界</text>
        <text class="empty-description">
          {{ searchQuery ? '没有找到匹配的世界' : '开始探索或创建你的第一个世界吧' }}
        </text>
        <BaseButton
          v-if="!searchQuery"
          type="primary"
          @click="handleCreateWorld"
        >
          创建世界
        </BaseButton>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <BaseLoading text="加载中..." />
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/modules/app'
import { BaseInput, BaseButton, BaseLoading } from '@/components'

// 世界接口定义
interface World {
  id: string
  name: string
  subtitle: string
  description: string
  banner: string
  category: string
  tags: string[]
  status: 'active' | 'exploring' | 'completed'
  explorationProgress: number
  locations: number
  characters: number
  stories: number
  favorited: boolean
  lastVisitAt: number
  createdAt: number
}

const appStore = useAppStore()

// 状态
const searchQuery = ref('')
const currentCategory = ref('all')
const loading = ref(false)
const worlds = ref<World[]>([])

// 分类选项
const categories = [
  { key: 'all', label: '全部', icon: '🌍' },
  { key: 'fantasy', label: '奇幻', icon: '🧙‍♂️' },
  { key: 'scifi', label: '科幻', icon: '🚀' },
  { key: 'modern', label: '现代', icon: '🏙️' },
  { key: 'historical', label: '历史', icon: '🏛️' },
  { key: 'mystery', label: '悬疑', icon: '🔍' }
]

// 过滤后的世界列表
const filteredWorlds = computed(() => {
  let result = worlds.value

  // 按分类过滤
  if (currentCategory.value !== 'all') {
    result = result.filter(world => world.category === currentCategory.value)
  }

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(world =>
      world.name.toLowerCase().includes(query) ||
      world.description.toLowerCase().includes(query) ||
      world.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 按最近访问时间排序
  return result.sort((a, b) => b.lastVisitAt - a.lastVisitAt)
})

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap = {
    active: '活跃',
    exploring: '探索中',
    completed: '已完成'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

// 设置分类
const setCategory = (categoryKey: string) => {
  currentCategory.value = categoryKey
}

// 进入世界
const handleEnterWorld = (world: World) => {
  appStore.navigateTo(`/pages/home/<USER>/explorer?worldId=${world.id}`)
}

// 收藏/取消收藏
const handleFavorite = (world: World) => {
  world.favorited = !world.favorited
  appStore.showToast(world.favorited ? '已收藏' : '已取消收藏', 'success')
}

// 分享世界
const handleShare = (world: World) => {
  uni.share({
    provider: 'weixin',
    type: 0,
    title: `分享世界：${world.name}`,
    summary: world.description,
    success: () => {
      appStore.showToast('分享成功', 'success')
    },
    fail: () => {
      appStore.showToast('分享失败', 'error')
    }
  })
}

// 查看详情
const handleViewDetails = (world: World) => {
  appStore.navigateTo(`/pages/world/detail?worldId=${world.id}`)
}

// 创建世界
const handleCreateWorld = () => {
  appStore.navigateTo('/pages/creation/world-editor')
}

// 加载世界数据
const loadWorlds = async () => {
  try {
    loading.value = true
    
    // TODO: 调用API获取用户的世界列表
    // const response = await worldApi.getUserWorlds()
    // worlds.value = response.data
    
    // 临时数据
    worlds.value = [
      {
        id: '1',
        name: '阿尔卡迪亚',
        subtitle: '魔法与科技并存的世界',
        description: '一个充满魔法与科技奇迹的世界，古老的魔法学院与现代化的城市和谐共存，等待着勇敢的探险者来发现其中的秘密。',
        banner: '/static/images/world-banner-1.jpg',
        category: 'fantasy',
        tags: ['魔法', '科技', '冒险', '学院'],
        status: 'exploring',
        explorationProgress: 45,
        locations: 12,
        characters: 8,
        stories: 5,
        favorited: true,
        lastVisitAt: Date.now() - 3600000,
        createdAt: Date.now() - 86400000 * 10
      },
      {
        id: '2',
        name: '新东京2087',
        subtitle: '赛博朋克未来都市',
        description: '2087年的新东京，霓虹灯闪烁的摩天大楼间隐藏着无数秘密，人工智能与人类共存，科技与人性的边界变得模糊。',
        banner: '/static/images/world-banner-2.jpg',
        category: 'scifi',
        tags: ['赛博朋克', '未来', 'AI', '都市'],
        status: 'active',
        explorationProgress: 20,
        locations: 8,
        characters: 15,
        stories: 3,
        favorited: false,
        lastVisitAt: Date.now() - 86400000,
        createdAt: Date.now() - 86400000 * 5
      }
    ]
  } catch (error) {
    appStore.showToast('加载失败', 'error')
    console.error('Failed to load worlds:', error)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadWorlds()
})
</script>

<style lang="scss" scoped>
.world-panel {
  height: 100%;
  background-color: var(--bg-secondary);
}

.world-list {
  height: 100%;
  padding: var(--spacing-md);
}

.search-section {
  margin-bottom: var(--spacing-lg);
  
  .category-tabs {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);
    
    .category-tab {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius-md);
      background-color: var(--bg-tertiary);
      white-space: nowrap;
      transition: all 0.2s ease;
      
      .category-icon {
        font-size: 16px;
      }
      
      .category-text {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
      }
      
      &.active {
        background-color: var(--primary-color);
        
        .category-text {
          color: white;
        }
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  }
}

.world-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.world-card {
  @include card-hover;
  overflow: hidden;
  cursor: pointer;
  
  .card-header {
    position: relative;
    height: 160px;
    overflow: hidden;
    
    .world-banner {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .world-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
      padding: var(--spacing-md);
      color: white;
      
      .world-title-section {
        margin-bottom: var(--spacing-xs);
        
        .world-title {
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-bold);
          display: block;
          margin-bottom: 2px;
        }
        
        .world-subtitle {
          font-size: var(--font-size-sm);
          opacity: 0.9;
        }
      }
      
      .world-status {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        
        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          
          &.active {
            background-color: var(--success-color);
          }
          
          &.exploring {
            background-color: var(--warning-color);
          }
          
          &.completed {
            background-color: var(--text-tertiary);
          }
        }
        
        .status-text {
          font-size: var(--font-size-xs);
          opacity: 0.8;
        }
      }
    }
  }
  
  .card-content {
    padding: var(--spacing-md);
    
    .world-description {
      @include text-ellipsis-multiline(3);
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-normal);
      margin-bottom: var(--spacing-sm);
    }
    
    .world-tags {
      display: flex;
      gap: var(--spacing-xs);
      flex-wrap: wrap;
      margin-bottom: var(--spacing-md);
      
      .world-tag {
        background-color: var(--bg-tertiary);
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        padding: 2px 6px;
        border-radius: var(--border-radius-sm);
      }
    }
    
    .world-stats {
      .stat-group {
        margin-bottom: var(--spacing-sm);
        
        .stat-label {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
          display: block;
          margin-bottom: 4px;
        }
        
        .progress-bar {
          height: 6px;
          background-color: var(--bg-tertiary);
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 4px;
          
          .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
          }
        }
        
        .stat-value {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
          font-weight: var(--font-weight-medium);
        }
      }
      
      .stat-row {
        display: flex;
        justify-content: space-between;
        
        .stat-item {
          display: flex;
          align-items: center;
          gap: 2px;
          
          .stat-icon {
            font-size: 12px;
          }
          
          .stat-text {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }
      }
    }
  }
  
  .card-actions {
    @include flex-between;
    padding: var(--spacing-md);
    border-top: 1px solid var(--separator);
    
    .action-buttons {
      display: flex;
      gap: var(--spacing-xs);
      
      .action-btn {
        @include flex-center;
        width: 32px;
        height: 32px;
        border-radius: var(--border-radius-sm);
        transition: background-color 0.2s ease;
        
        &:active {
          background-color: var(--bg-secondary);
        }
        
        .action-icon {
          font-size: 16px;
        }
      }
    }
  }
}

.empty-state {
  @include flex-column-center;
  padding: var(--spacing-2xl);
  text-align: center;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
  }
  
  .empty-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }
  
  .empty-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-lg);
    line-height: var(--line-height-normal);
  }
}

.loading-state {
  @include flex-center;
  padding: var(--spacing-2xl);
}

// 响应式适配
@include respond-to('small') {
  .world-grid {
    grid-template-columns: 1fr;
  }
  
  .world-card {
    .card-header {
      height: 120px;
    }
    
    .card-content,
    .card-actions {
      padding: var(--spacing-sm);
    }
  }
}
</style>
