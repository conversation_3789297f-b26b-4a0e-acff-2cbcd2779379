// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const valueParser = require('postcss-value-parser');
const validateTypes = require('../../utils/validateTypes.cjs');
const nodeFieldIndices = require('../../utils/nodeFieldIndices.cjs');
const isCustomProperty = require('../../utils/isCustomProperty.cjs');
const isStandardSyntaxFunction = require('../../utils/isStandardSyntaxFunction.cjs');
const matchesStringOrRegExp = require('../../utils/matchesStringOrRegExp.cjs');
const report = require('../../utils/report.cjs');
const ruleMessages = require('../../utils/ruleMessages.cjs');
const validateOptions = require('../../utils/validateOptions.cjs');
const vendor = require('../../utils/vendor.cjs');

const ruleName = 'function-allowed-list';

const messages = ruleMessages(ruleName, {
	rejected: (name) => `Unexpected function "${name}"`,
});

const meta = {
	url: 'https://stylelint.io/user-guide/rules/function-allowed-list',
};

/** @type {import('stylelint').CoreRules[ruleName]} */
const rule = (primary, secondaryOptions) => {
	return (root, result) => {
		const validOptions = validateOptions(
			result,
			ruleName,
			{
				actual: primary,
				possible: [validateTypes.isString, validateTypes.isRegExp],
			},
			{
				actual: secondaryOptions,
				possible: {
					exceptWithoutPropertyFallback: [validateTypes.isString, validateTypes.isRegExp],
				},
				optional: true,
			},
		);

		if (!validOptions) {
			return;
		}

		const exceptWithoutPropertyFallback = secondaryOptions?.exceptWithoutPropertyFallback ?? [];

		root.walkDecls((decl) => {
			valueParser(decl.value).walk((node) => {
				if (node.type !== 'function') {
					return;
				}

				if (!isStandardSyntaxFunction(node)) {
					return;
				}

				const unprefixedName = vendor.unprefixed(node.value);

				if (matchesStringOrRegExp(unprefixedName, primary)) {
					// Check if function requires fallback
					if (!matchesStringOrRegExp(unprefixedName, exceptWithoutPropertyFallback)) {
						return;
					}

					if (hasPrevPropertyDeclaration(decl)) return;
				}

				const index = nodeFieldIndices.declarationValueIndex(decl) + node.sourceIndex;
				const endIndex = index + node.value.length;

				report({
					message: messages.rejected,
					messageArgs: [node.value],
					node: decl,
					index,
					endIndex,
					result,
					ruleName,
				});
			});
		});
	};
};

/**
 * @param {import('postcss').Declaration} decl
 * @returns {boolean}
 */
function hasPrevPropertyDeclaration(decl) {
	const prop = decl.prop.toLowerCase();

	if (isCustomProperty(prop)) return false;

	let prev = decl.prev();

	while (prev) {
		if (prev.type === 'decl' && prev.prop.toLowerCase() === prop) {
			return true;
		}

		prev = prev.prev();
	}

	return false;
}

rule.primaryOptionArray = true;

rule.ruleName = ruleName;
rule.messages = messages;
rule.meta = meta;

module.exports = rule;
