'use strict';

var url = require('url');

/**
 * Path to CSS functions list JSON file.
 */
const location = url.fileURLToPath(new url.URL('index.json', (typeof document === 'undefined' ? new (require('u' + 'rl').URL)('file:' + __filename).href : (document.currentScript && document.currentScript.src || new URL('index.js', document.baseURI).href))));

module.exports = location;
//# sourceMappingURL=index.js.map
