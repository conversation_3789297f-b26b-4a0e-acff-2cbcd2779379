// Pinia stores 入口文件

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// 创建 pinia 实例
export const pinia = createPinia()

// 使用持久化插件
pinia.use(piniaPluginPersistedstate)

// 导出所有 stores
export * from './modules/app'
export * from './modules/user'
export * from './modules/auth'
export * from './modules/chat'
export * from './modules/novel'
export * from './modules/world'
export * from './modules/creation'
export * from './modules/discovery'
export * from './modules/forum'
export * from './modules/settings'
