import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { LoadingState, ThemeConfig, DeviceInfo } from '@/types'

export const useAppStore = defineStore('app', () => {
  // State
  const initialized = ref(false)
  const loading = ref<LoadingState>('idle')
  const visible = ref(true)
  const networkStatus = ref<'online' | 'offline'>('online')
  const theme = ref<ThemeConfig>({
    mode: 'auto',
    primaryColor: '#007AFF',
    accentColor: '#5856D6',
    fontSize: 'medium'
  })
  const deviceInfo = ref<DeviceInfo>({
    platform: '',
    version: '',
    model: '',
    brand: ''
  })
  const systemInfo = ref<any>(null)
  const statusBarHeight = ref(0)
  const navigationBarHeight = ref(44)
  const tabBarHeight = ref(50)
  const safeAreaInsets = ref({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  })

  // Getters
  const isLoading = computed(() => loading.value === 'loading')
  const isOnline = computed(() => networkStatus.value === 'online')
  const isDarkMode = computed(() => {
    if (theme.value.mode === 'auto') {
      // 根据系统主题判断
      return systemInfo.value?.theme === 'dark'
    }
    return theme.value.mode === 'dark'
  })
  const contentHeight = computed(() => {
    if (!systemInfo.value) return 0
    return systemInfo.value.windowHeight - statusBarHeight.value - navigationBarHeight.value - tabBarHeight.value
  })

  // Actions
  const initialize = async () => {
    if (initialized.value) return

    try {
      loading.value = 'loading'
      
      // 获取系统信息
      await getSystemInfo()
      
      // 获取设备信息
      await getDeviceInfo()
      
      // 监听网络状态
      setupNetworkListener()
      
      // 设置主题
      applyTheme()
      
      initialized.value = true
      loading.value = 'success'
    } catch (error) {
      console.error('App initialization failed:', error)
      loading.value = 'error'
    }
  }

  const getSystemInfo = async () => {
    return new Promise((resolve) => {
      uni.getSystemInfo({
        success: (res) => {
          systemInfo.value = res
          statusBarHeight.value = res.statusBarHeight || 0
          
          // 设置安全区域
          if (res.safeAreaInsets) {
            safeAreaInsets.value = res.safeAreaInsets
          }
          
          resolve(res)
        },
        fail: (error) => {
          console.error('Failed to get system info:', error)
          resolve(null)
        }
      })
    })
  }

  const getDeviceInfo = async () => {
    return new Promise((resolve) => {
      uni.getDeviceInfo({
        success: (res) => {
          deviceInfo.value = {
            platform: res.platform || '',
            version: res.system || '',
            model: res.model || '',
            brand: res.brand || ''
          }
          resolve(res)
        },
        fail: (error) => {
          console.error('Failed to get device info:', error)
          resolve(null)
        }
      })
    })
  }

  const setupNetworkListener = () => {
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      networkStatus.value = res.isConnected ? 'online' : 'offline'
      
      if (!res.isConnected) {
        showToast('网络连接已断开')
      }
    })
    
    // 获取当前网络状态
    uni.getNetworkType({
      success: (res) => {
        networkStatus.value = res.networkType === 'none' ? 'offline' : 'online'
      }
    })
  }

  const applyTheme = () => {
    // 应用主题配置
    const root = document.documentElement
    if (root) {
      root.style.setProperty('--primary-color', theme.value.primaryColor)
      root.style.setProperty('--accent-color', theme.value.accentColor)
      
      // 设置字体大小
      const fontSizeMap = {
        small: '14px',
        medium: '16px',
        large: '18px'
      }
      root.style.setProperty('--font-size-base', fontSizeMap[theme.value.fontSize])
    }
  }

  const setAppVisible = (isVisible: boolean) => {
    visible.value = isVisible
  }

  const setLoading = (state: LoadingState) => {
    loading.value = state
  }

  const setTheme = (newTheme: Partial<ThemeConfig>) => {
    theme.value = { ...theme.value, ...newTheme }
    applyTheme()
  }

  const showToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none') => {
    uni.showToast({
      title,
      icon,
      duration: 2000
    })
  }

  const showLoading = (title = '加载中...') => {
    uni.showLoading({ title })
  }

  const hideLoading = () => {
    uni.hideLoading()
  }

  const showModal = (options: {
    title?: string
    content: string
    showCancel?: boolean
    cancelText?: string
    confirmText?: string
  }) => {
    return new Promise<boolean>((resolve) => {
      uni.showModal({
        title: options.title || '提示',
        content: options.content,
        showCancel: options.showCancel ?? true,
        cancelText: options.cancelText || '取消',
        confirmText: options.confirmText || '确定',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  const navigateTo = (url: string) => {
    uni.navigateTo({ url })
  }

  const redirectTo = (url: string) => {
    uni.redirectTo({ url })
  }

  const switchTab = (url: string) => {
    uni.switchTab({ url })
  }

  const navigateBack = (delta = 1) => {
    uni.navigateBack({ delta })
  }

  return {
    // State
    initialized,
    loading,
    visible,
    networkStatus,
    theme,
    deviceInfo,
    systemInfo,
    statusBarHeight,
    navigationBarHeight,
    tabBarHeight,
    safeAreaInsets,
    
    // Getters
    isLoading,
    isOnline,
    isDarkMode,
    contentHeight,
    
    // Actions
    initialize,
    setAppVisible,
    setLoading,
    setTheme,
    showToast,
    showLoading,
    hideLoading,
    showModal,
    navigateTo,
    redirectTo,
    switchTab,
    navigateBack
  }
}, {
  persist: {
    key: 'app-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value)
    },
    paths: ['theme', 'initialized']
  }
})
