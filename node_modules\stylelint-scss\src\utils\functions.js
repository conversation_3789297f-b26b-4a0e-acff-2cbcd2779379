"use strict";

/**
 * @see https://sass-lang.com/documentation/modules#global-functions
 */
const GLOBAL_FUNCTIONS = Object.freeze(["hsl", "hsla", "if", "rgb", "rgba"]);

/**
 * @see https://sass-lang.com/documentation/modules/color
 */
const COLOR_FUNCTIONS = Object.freeze([
  "adjust-color",
  "adjust-hue",
  "alpha",
  "blackness",
  "blue",
  "change-color",
  "complement",
  "darken",
  "desaturate",
  "fade-in",
  "fade-out",
  "grayscale",
  "green",
  "hue",
  "ie-hex-str",
  "invert",
  "lighten",
  "lightness",
  "mix",
  "opacify",
  "opacity",
  "red",
  "saturate",
  "saturation",
  "scale-color",
  "transparentize",

  // with namespace
  "color.adjust",
  "color.alpha",
  "color.blackness",
  "color.blue",
  "color.change",
  "color.channel",
  "color.complement",
  "color.grayscale",
  "color.green",
  "color.hue",
  "color.hwb",
  "color.ie-hex-str",
  "color.invert",
  "color.is-legacy",
  "color.is-missing",
  "color.is-powerless",
  "color.lightness",
  "color.mix",
  "color.red",
  "color.same",
  "color.saturation",
  "color.scale",
  "color.space",
  "color.to-gamut",
  "color.to-space",
  "color.whiteness"
]);

/**
 * @see https://sass-lang.com/documentation/modules/list
 */
const LIST_FUNCTIONS = Object.freeze([
  "append",
  "index",
  "is-bracketed",
  "join",
  "length",
  "list-separator",
  "nth",
  "set-nth",
  "list.zip",

  // with namespace
  "list.append",
  "list.index",
  "list.is-bracketed",
  "list.join",
  "list.length",
  "list.nth",
  "list.separator",
  "list.set-nth",
  "list.slash",
  "zip"
]);

/**
 * @see https://sass-lang.com/documentation/modules/map
 */
const MAP_FUNCTIONS = Object.freeze([
  "map-get",
  "map-has-key",
  "map-keys",
  "map-merge",
  "map-remove",
  "map-values",

  // with namespace
  "map.deep-merge",
  "map.deep-remove",
  "map.get",
  "map.has-key",
  "map.keys",
  "map.merge",
  "map.remove",
  "map.set",
  "map.values"
]);

/**
 * @see https://sass-lang.com/documentation/modules/math
 */
const MATH_FUNCTIONS = Object.freeze([
  "abs",
  "ceil",
  "comparable",
  "floor",
  "max",
  "min",
  "percentage",
  "random",
  "round",
  "unit",
  "unitless",

  // with namespace
  "math.abs",
  "math.acos",
  "math.asin",
  "math.atan",
  "math.atan2",
  "math.ceil",
  "math.clamp",
  "math.compatible",
  "math.cos",
  "math.div",
  "math.floor",
  "math.hypot",
  "math.is-unitless",
  "math.log",
  "math.max",
  "math.min",
  "math.percentage",
  "math.pow",
  "math.random",
  "math.round",
  "math.sin",
  "math.sqrt",
  "math.tan",
  "math.unit"
]);

/**
 * @see https://sass-lang.com/documentation/modules/meta
 */
const META_FUNCTIONS = Object.freeze([
  "call",
  "content-exists",
  "feature-exists",
  "function-exists",
  "get-function",
  "global-variable-exists",
  "inspect",
  "keywords",
  "mixin-exists",
  "type-of",
  "variable-exists",

  // with namespace
  "meta.accepts-content",
  "meta.apply",
  "meta.calc-args",
  "meta.calc-name",
  "meta.call",
  "meta.content-exists",
  "meta.feature-exists",
  "meta.function-exists",
  "meta.get-function",
  "meta.get-mixin",
  "meta.global-variable-exists",
  "meta.inspect",
  "meta.keywords",
  "meta.load-css",
  "meta.mixin-exists",
  "meta.module-functions",
  "meta.module-mixins",
  "meta.module-variables",
  "meta.type-of",
  "meta.variable-exists"
]);

/**
 * @see https://sass-lang.com/documentation/modules/selector
 */
const SELECTOR_FUNCTIONS = Object.freeze([
  "is-superselector",
  "selector-append",
  "selector-extend",
  "selector-nest",
  "selector-parse",
  "selector-replace",
  "selector-unify",
  "simple-selectors",

  // with namespace
  "selector.append",
  "selector.extend",
  "selector.is-superselector",
  "selector.nest",
  "selector.parse",
  "selector.replace",
  "selector.simple-selectors",
  "selector.unify"
]);

/**
 * @see https://sass-lang.com/documentation/modules/string
 */
const STRING_FUNCTIONS = Object.freeze([
  "quote",
  "str-index",
  "str-insert",
  "str-length",
  "str-slice",
  "to-lower-case",
  "to-upper-case",
  "unique-id",
  "unquote",

  // with namespace
  "string.index",
  "string.insert",
  "string.length",
  "string.quote",
  "string.slice",
  "string.split",
  "string.to-lower-case",
  "string.to-upper-case",
  "string.unique-id",
  "string.unquote"
]);

const ALL_FUNCTIONS = Object.freeze([
  ...GLOBAL_FUNCTIONS,
  ...COLOR_FUNCTIONS,
  ...LIST_FUNCTIONS,
  ...MAP_FUNCTIONS,
  ...MATH_FUNCTIONS,
  ...META_FUNCTIONS,
  ...SELECTOR_FUNCTIONS,
  ...STRING_FUNCTIONS
]);

module.exports = {
  GLOBAL_FUNCTIONS,
  COLOR_FUNCTIONS,
  LIST_FUNCTIONS,
  MAP_FUNCTIONS,
  MATH_FUNCTIONS,
  META_FUNCTIONS,
  SELECTOR_FUNCTIONS,
  STRING_FUNCTIONS,
  ALL_FUNCTIONS
};
