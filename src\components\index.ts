// 组件库导出文件

// 基础组件
export { default as BaseButton } from './common/base/BaseButton.vue'
export { default as BaseInput } from './common/base/BaseInput.vue'
export { default as BaseLoading } from './common/base/BaseLoading.vue'
export { default as BaseModal } from './common/base/BaseModal.vue'

// 布局组件
// export { default as AppHeader } from './common/layout/AppHeader.vue'
// export { default as AppFooter } from './common/layout/AppFooter.vue'
// export { default as AppTabBar } from './common/layout/AppTabBar.vue'

// 聊天组件
// export { default as ChatMessage } from './chat/ChatMessage.vue'
// export { default as ChatInput } from './chat/ChatInput.vue'
// export { default as CharacterCard } from './chat/CharacterCard.vue'

// 小说组件
// export { default as NovelReader } from './novel/NovelReader.vue'
// export { default as NovelChapter } from './novel/NovelChapter.vue'

// 世界组件
// export { default as WorldMap } from './world/WorldMap.vue'
// export { default as WorldTimeline } from './world/WorldTimeline.vue'
