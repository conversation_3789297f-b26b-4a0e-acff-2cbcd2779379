// NextNovel App 基础样式

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

// 按钮重置
button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

// 输入框重置
input,
textarea {
  font: inherit;
  color: inherit;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// 列表样式
ul,
ol {
  list-style: none;
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-tertiary {
  background-color: var(--bg-tertiary);
}

// Flex 工具类
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

// 间距工具类
@include generate-spacing('margin', 'm');
@include generate-spacing('margin-top', 'mt');
@include generate-spacing('margin-right', 'mr');
@include generate-spacing('margin-bottom', 'mb');
@include generate-spacing('margin-left', 'ml');
@include generate-spacing('padding', 'p');
@include generate-spacing('padding-top', 'pt');
@include generate-spacing('padding-right', 'pr');
@include generate-spacing('padding-bottom', 'pb');
@include generate-spacing('padding-left', 'pl');

// 圆角工具类
.rounded-sm {
  border-radius: var(--border-radius-sm);
}

.rounded-md {
  border-radius: var(--border-radius-md);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.rounded-xl {
  border-radius: var(--border-radius-xl);
}

.rounded-full {
  border-radius: var(--border-radius-full);
}

// 阴影工具类
.shadow-light {
  box-shadow: var(--shadow-light);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-heavy {
  box-shadow: var(--shadow-heavy);
}

// 隐藏/显示
.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

// 溢出处理
.overflow-hidden {
  overflow: hidden;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-auto {
  overflow: auto;
}

// 文本省略
.text-ellipsis {
  @include text-ellipsis;
}

.text-ellipsis-2 {
  @include text-ellipsis-multiline(2);
}

.text-ellipsis-3 {
  @include text-ellipsis-multiline(3);
}

// 位置
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

// 宽高
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

// 动画类
.fade-in {
  @include fade-in;
}

.slide-up {
  @include slide-up;
}

.bounce-in {
  @include bounce-in;
}

// 过渡效果
.transition {
  transition: all 0.2s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.2s ease;
}

// 交互状态
.hover-scale:hover {
  transform: scale(1.05);
}

.active-scale:active {
  transform: scale(0.95);
}

// 安全区域适配
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
