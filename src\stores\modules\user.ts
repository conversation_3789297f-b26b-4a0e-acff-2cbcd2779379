import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ID, Statistics } from '@/types'

// 用户信息接口
export interface UserProfile {
  id: ID
  username: string
  nickname: string
  avatar: string
  email?: string
  phone?: string
  bio?: string
  location?: string
  website?: string
  birthdate?: string
  gender?: 'male' | 'female' | 'other'
  verified: boolean
  level: number
  experience: number
  createdAt: number
  updatedAt: number
}

// 用户统计信息
export interface UserStatistics extends Statistics {
  followers: number
  following: number
  creations: number
  collections: number
}

// 用户设置
export interface UserSettings {
  privacy: {
    profileVisible: boolean
    emailVisible: boolean
    phoneVisible: boolean
  }
  notifications: {
    email: boolean
    push: boolean
    inApp: boolean
    marketing: boolean
  }
  preferences: {
    language: string
    timezone: string
    autoSave: boolean
    darkMode: boolean
  }
}

export const useUserStore = defineStore('user', () => {
  // State
  const profile = ref<UserProfile | null>(null)
  const statistics = ref<UserStatistics>({
    views: 0,
    likes: 0,
    shares: 0,
    comments: 0,
    downloads: 0,
    followers: 0,
    following: 0,
    creations: 0,
    collections: 0
  })
  const settings = ref<UserSettings>({
    privacy: {
      profileVisible: true,
      emailVisible: false,
      phoneVisible: false
    },
    notifications: {
      email: true,
      push: true,
      inApp: true,
      marketing: false
    },
    preferences: {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      autoSave: true,
      darkMode: false
    }
  })
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isLoggedIn = computed(() => !!profile.value)
  const userId = computed(() => profile.value?.id)
  const userAvatar = computed(() => profile.value?.avatar || '/static/images/default-avatar.png')
  const userDisplayName = computed(() => profile.value?.nickname || profile.value?.username || '未知用户')
  const userLevel = computed(() => profile.value?.level || 1)
  const isVerified = computed(() => profile.value?.verified || false)

  // Actions
  const setProfile = (userProfile: UserProfile) => {
    profile.value = userProfile
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!profile.value) return

    try {
      loading.value = true
      error.value = null

      // TODO: 调用 API 更新用户信息
      // const updatedProfile = await userApi.updateProfile(updates)
      
      // 临时更新本地状态
      profile.value = { ...profile.value, ...updates, updatedAt: Date.now() }
      
      return profile.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateAvatar = async (avatarFile: File) => {
    if (!profile.value) return

    try {
      loading.value = true
      error.value = null

      // TODO: 上传头像文件
      // const avatarUrl = await uploadService.uploadAvatar(avatarFile)
      // await updateProfile({ avatar: avatarUrl })
      
      // 临时处理
      const avatarUrl = URL.createObjectURL(avatarFile)
      await updateProfile({ avatar: avatarUrl })
      
      return avatarUrl
    } catch (err) {
      error.value = err instanceof Error ? err.message : '头像上传失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    try {
      loading.value = true
      error.value = null

      // TODO: 调用 API 更新设置
      // await userApi.updateSettings(newSettings)
      
      // 更新本地设置
      settings.value = { ...settings.value, ...newSettings }
      
      return settings.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : '设置更新失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchStatistics = async () => {
    if (!profile.value) return

    try {
      loading.value = true
      error.value = null

      // TODO: 调用 API 获取统计信息
      // const stats = await userApi.getStatistics(profile.value.id)
      // statistics.value = stats
      
      // 临时数据
      statistics.value = {
        views: Math.floor(Math.random() * 10000),
        likes: Math.floor(Math.random() * 1000),
        shares: Math.floor(Math.random() * 100),
        comments: Math.floor(Math.random() * 500),
        downloads: Math.floor(Math.random() * 200),
        followers: Math.floor(Math.random() * 1000),
        following: Math.floor(Math.random() * 500),
        creations: Math.floor(Math.random() * 50),
        collections: Math.floor(Math.random() * 100)
      }
      
      return statistics.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取统计信息失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const followUser = async (targetUserId: ID) => {
    try {
      loading.value = true
      error.value = null

      // TODO: 调用 API 关注用户
      // await userApi.followUser(targetUserId)
      
      // 更新统计信息
      statistics.value.following += 1
      
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '关注失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const unfollowUser = async (targetUserId: ID) => {
    try {
      loading.value = true
      error.value = null

      // TODO: 调用 API 取消关注
      // await userApi.unfollowUser(targetUserId)
      
      // 更新统计信息
      statistics.value.following = Math.max(0, statistics.value.following - 1)
      
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '取消关注失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearProfile = () => {
    profile.value = null
    statistics.value = {
      views: 0,
      likes: 0,
      shares: 0,
      comments: 0,
      downloads: 0,
      followers: 0,
      following: 0,
      creations: 0,
      collections: 0
    }
    error.value = null
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    profile,
    statistics,
    settings,
    loading,
    error,
    
    // Getters
    isLoggedIn,
    userId,
    userAvatar,
    userDisplayName,
    userLevel,
    isVerified,
    
    // Actions
    setProfile,
    updateProfile,
    updateAvatar,
    updateSettings,
    fetchStatistics,
    followUser,
    unfollowUser,
    clearProfile,
    clearError
  }
}, {
  persist: {
    key: 'user-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value)
    },
    paths: ['profile', 'settings']
  }
})
