# NEXTNOVEL APP 依赖管理和构建系统

## 项目依赖配置

### 1. package.json 配置

```json
{
  "name": "nextnovel-app",
  "version": "1.0.0",
  "description": "AI驱动的创意内容移动应用",
  "main": "main.ts",
  "scripts": {
    "dev": "uni",
    "dev:h5": "uni --platform h5",
    "dev:mp-weixin": "uni --platform mp-weixin",
    "dev:app": "uni --platform app",
    "build": "uni build",
    "build:h5": "uni build --platform h5",
    "build:mp-weixin": "uni build --platform mp-weixin",
    "build:app": "uni build --platform app",
    "type-check": "vue-tsc --noEmit",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "lint:style": "stylelint **/*.{vue,css,scss} --fix",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "preview": "vite preview"
  },
  "dependencies": {
    "@dcloudio/uni-app": "^3.0.0-4010920240507001",
    "@dcloudio/uni-components": "^3.0.0-4010920240507001",
    "@dcloudio/uni-h5": "^3.0.0-4010920240507001",
    "@dcloudio/uni-mp-weixin": "^3.0.0-4010920240507001",
    "@dcloudio/uni-app-plus": "^3.0.0-4010920240507001",
    "vue": "^3.4.0",
    "pinia": "^2.1.7",
    "pinia-plugin-persistedstate": "^3.2.1",
    "axios": "^1.6.0",
    "dayjs": "^1.11.10",
    "lodash-es": "^4.17.21",
    "crypto-js": "^4.2.0",
    "socket.io-client": "^4.7.4"
  },
  "devDependencies": {
    "@dcloudio/types": "^3.4.8",
    "@dcloudio/uni-automator": "^3.0.0-4010920240507001",
    "@dcloudio/uni-cli-shared": "^3.0.0-4010920240507001",
    "@dcloudio/vite-plugin-uni": "^3.0.0-4010920240507001",
    "@types/crypto-js": "^4.2.1",
    "@types/lodash-es": "^4.17.12",
    "@types/node": "^20.10.0",
    "@typescript-eslint/eslint-plugin": "^6.14.0",
    "@typescript-eslint/parser": "^6.14.0",
    "@vitejs/plugin-vue": "^4.5.2",
    "@vue/eslint-config-prettier": "^8.0.0",
    "@vue/eslint-config-typescript": "^12.0.0",
    "@vue/test-utils": "^2.4.3",
    "@vue/tsconfig": "^0.5.1",
    "eslint": "^8.55.0",
    "eslint-plugin-vue": "^9.19.2",
    "jsdom": "^23.0.1",
    "prettier": "^3.1.1",
    "sass": "^1.69.5",
    "stylelint": "^16.0.2",
    "stylelint-config-standard-scss": "^12.0.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "typescript": "^5.3.3",
    "vite": "^5.0.10",
    "vitest": "^1.0.4",
    "vue-tsc": "^1.8.25"
  }
}
```

### 2. 核心依赖说明

#### 框架依赖
- **@dcloudio/uni-app**: UniApp 核心框架
- **vue**: Vue 3 框架
- **pinia**: 状态管理库
- **@dcloudio/vite-plugin-uni**: UniApp Vite 插件

#### 工具库依赖
- **axios**: HTTP 请求库
- **dayjs**: 日期处理库
- **lodash-es**: 工具函数库
- **crypto-js**: 加密解密库
- **socket.io-client**: WebSocket 客户端

#### 开发依赖
- **typescript**: TypeScript 支持
- **eslint**: 代码检查工具
- **prettier**: 代码格式化工具
- **vitest**: 测试框架
- **sass**: CSS 预处理器

## 构建配置

### 1. Vite 配置 (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

export default defineConfig({
  plugins: [uni()],
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@services': resolve(__dirname, 'src/services'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  // CSS 配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @import "@/assets/styles/variables.scss";
          @import "@/assets/styles/mixins.scss";
        `
      }
    }
  },
  
  // 构建配置
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'static',
    sourcemap: process.env.NODE_ENV === 'development',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production'
      }
    },
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  },
  
  // 开发服务器配置
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // 环境变量配置
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
})
```

### 2. TypeScript 配置 (tsconfig.json)

```json
{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@pages/*": ["src/pages/*"],
      "@stores/*": ["src/stores/*"],
      "@services/*": ["src/services/*"],
      "@utils/*": ["src/utils/*"],
      "@types/*": ["src/types/*"],
      "@assets/*": ["src/assets/*"]
    },
    "types": [
      "@dcloudio/types",
      "vite/client",
      "node"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js"
  ]
}
```

## 代码质量配置

### 1. ESLint 配置 (.eslintrc.js)

```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier',
    'plugin:vue/vue3-essential'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: ['vue', '@typescript-eslint'],
  rules: {
    // Vue 规则
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-vars': 'error',
    'vue/no-mutating-props': 'error',
    'vue/require-default-prop': 'error',
    'vue/require-prop-types': 'error',
    
    // TypeScript 规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error'
  },
  globals: {
    uni: 'readonly',
    wx: 'readonly',
    getCurrentPages: 'readonly',
    getApp: 'readonly'
  }
}
```

### 2. Prettier 配置 (.prettierrc)

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "vueIndentScriptAndStyle": false
}
```

### 3. Stylelint 配置 (.stylelintrc.js)

```javascript
module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-standard-vue'
  ],
  rules: {
    'selector-class-pattern': '^[a-z][a-z0-9]*(-[a-z0-9]+)*(__[a-z0-9]+(-[a-z0-9]+)*)?(--[a-z0-9]+(-[a-z0-9]+)*)?$',
    'scss/at-rule-no-unknown': [
      true,
      {
        'ignoreAtRules': ['tailwind', 'apply', 'variants', 'responsive', 'screen']
      }
    ],
    'declaration-block-trailing-semicolon': null,
    'no-descending-specificity': null
  }
}
```

## 环境管理

### 1. 环境变量配置

```bash
# .env (基础配置)
VITE_APP_TITLE=NextNovel
VITE_APP_VERSION=1.0.0

# .env.development (开发环境)
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:8080/api
VITE_AI_SERVICE_URL=http://localhost:8081
VITE_WS_URL=ws://localhost:8082
VITE_APP_DEBUG=true

# .env.production (生产环境)
NODE_ENV=production
VITE_API_BASE_URL=https://api.nextnovel.com
VITE_AI_SERVICE_URL=https://ai.nextnovel.com
VITE_WS_URL=wss://ws.nextnovel.com
VITE_APP_DEBUG=false

# .env.test (测试环境)
NODE_ENV=test
VITE_API_BASE_URL=http://test-api.nextnovel.com
VITE_AI_SERVICE_URL=http://test-ai.nextnovel.com
VITE_WS_URL=ws://test-ws.nextnovel.com
VITE_APP_DEBUG=true
```

### 2. 环境配置管理

```typescript
// src/utils/env.ts
interface EnvConfig {
  apiBaseUrl: string
  aiServiceUrl: string
  wsUrl: string
  debug: boolean
  version: string
}

const getEnvConfig = (): EnvConfig => {
  return {
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
    aiServiceUrl: import.meta.env.VITE_AI_SERVICE_URL,
    wsUrl: import.meta.env.VITE_WS_URL,
    debug: import.meta.env.VITE_APP_DEBUG === 'true',
    version: import.meta.env.VITE_APP_VERSION
  }
}

export const ENV = getEnvConfig()
```

## 构建优化

### 1. 代码分割策略

```typescript
// vite.config.ts 中的优化配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 第三方库分割
          'vendor-vue': ['vue', 'pinia'],
          'vendor-utils': ['axios', 'dayjs', 'lodash-es'],
          'vendor-ui': ['@dcloudio/uni-components'],
          
          // 业务模块分割
          'chat-module': [
            'src/components/chat',
            'src/stores/modules/chat.ts'
          ],
          'novel-module': [
            'src/components/novel',
            'src/stores/modules/novel.ts'
          ]
        }
      }
    }
  }
})
```

### 2. 资源优化

```typescript
// 图片压缩和优化
import { defineConfig } from 'vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

export default defineConfig({
  plugins: [
    // SVG 图标优化
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), 'src/assets/icons')],
      symbolId: 'icon-[dir]-[name]'
    })
  ],
  
  // 静态资源处理
  assetsInclude: ['**/*.woff', '**/*.woff2', '**/*.ttf'],
  
  build: {
    // 资源内联阈值
    assetsInlineLimit: 4096,
    
    // 压缩配置
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log']
      }
    }
  }
})
```

## 部署配置

### 1. CI/CD 配置 (.github/workflows/deploy.yml)

```yaml
name: Deploy

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run tests
        run: npm run test:coverage

  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        platform: [h5, mp-weixin, app]
    
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for ${{ matrix.platform }}
        run: npm run build:${{ matrix.platform }}
        env:
          NODE_ENV: production
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.platform }}
          path: dist/
```

### 2. Docker 配置

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build:h5

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. 部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署 NextNovel App..."

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 代码检查
echo "🔍 代码检查..."
npm run lint
npm run type-check

# 运行测试
echo "🧪 运行测试..."
npm run test

# 构建应用
echo "🏗️ 构建应用..."
npm run build:h5
npm run build:mp-weixin

# 部署到服务器
echo "🌐 部署到服务器..."
rsync -avz --delete dist/ user@server:/var/www/nextnovel/

echo "✅ 部署完成！"
```

---

*本文档定义了 NEXTNOVEL APP 的完整依赖管理和构建系统，确保项目的可维护性和部署效率。*
