<template>
  <view :class="loadingClasses">
    <!-- 加载动画 -->
    <view class="base-loading__spinner">
      <view v-if="type === 'spinner'" class="spinner-circle" />
      <view v-else-if="type === 'dots'" class="spinner-dots">
        <view class="dot" />
        <view class="dot" />
        <view class="dot" />
      </view>
      <view v-else-if="type === 'pulse'" class="spinner-pulse" />
      <view v-else-if="type === 'bars'" class="spinner-bars">
        <view class="bar" />
        <view class="bar" />
        <view class="bar" />
        <view class="bar" />
      </view>
    </view>
    
    <!-- 加载文本 -->
    <view v-if="text" class="base-loading__text">
      <text>{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 加载动画类型
  type?: 'spinner' | 'dots' | 'pulse' | 'bars'
  // 加载文本
  text?: string
  // 尺寸
  size?: 'small' | 'medium' | 'large'
  // 颜色
  color?: string
  // 是否垂直居中
  center?: boolean
  // 是否全屏覆盖
  overlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'medium',
  center: false,
  overlay: false
})

// 计算样式类
const loadingClasses = computed(() => [
  'base-loading',
  `base-loading--${props.size}`,
  {
    'base-loading--center': props.center,
    'base-loading--overlay': props.overlay
  }
])
</script>

<style lang="scss" scoped>
.base-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  
  // 尺寸变体
  &--small {
    .base-loading__spinner {
      width: 20px;
      height: 20px;
    }
    
    .base-loading__text {
      font-size: var(--font-size-xs);
    }
  }
  
  &--medium {
    .base-loading__spinner {
      width: 32px;
      height: 32px;
    }
    
    .base-loading__text {
      font-size: var(--font-size-sm);
    }
  }
  
  &--large {
    .base-loading__spinner {
      width: 48px;
      height: 48px;
    }
    
    .base-loading__text {
      font-size: var(--font-size-md);
    }
  }
  
  // 居中显示
  &--center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  // 全屏覆盖
  &--overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    
    @media (prefers-color-scheme: dark) {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
  
  // 加载动画容器
  &__spinner {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  // 加载文本
  &__text {
    color: var(--text-secondary);
    text-align: center;
  }
}

// 圆形旋转动画
.spinner-circle {
  width: 100%;
  height: 100%;
  border: 2px solid var(--bg-secondary);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 点状动画
.spinner-dots {
  display: flex;
  gap: 4px;
  
  .dot {
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
    
    &:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

// 脉冲动画
.spinner-pulse {
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

// 条状动画
.spinner-bars {
  display: flex;
  gap: 2px;
  align-items: end;
  height: 100%;
  
  .bar {
    width: 4px;
    background-color: var(--primary-color);
    animation: bars 1.2s ease-in-out infinite;
    
    &:nth-child(1) {
      animation-delay: -1.1s;
    }
    
    &:nth-child(2) {
      animation-delay: -1.0s;
    }
    
    &:nth-child(3) {
      animation-delay: -0.9s;
    }
    
    &:nth-child(4) {
      animation-delay: -0.8s;
    }
  }
}

// 动画关键帧
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes bars {
  0%, 40%, 100% {
    height: 20%;
  }
  20% {
    height: 100%;
  }
}
</style>
